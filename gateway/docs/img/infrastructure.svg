<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" style="background-color: rgb(255, 255, 255);" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="923px" height="1400px" viewBox="-0.5 -0.5 923 1400" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2020-10-05T19:56:23.592Z&quot; agent=&quot;5.0 (Macintosh)&quot; etag=&quot;qDqPaid4hTvO3vCnmu7e&quot; version=&quot;13.6.6&quot; type=&quot;google&quot;&gt;&lt;diagram id=&quot;r-vFajAFXQ9AcB83ZuYV&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"><defs><linearGradient x1="0%" y1="0%" x2="0%" y2="100%" id="mx-gradient-232f3e-100-232f3e-100-s-0"><stop offset="0%" style="stop-color:#232F3E"/><stop offset="100%" style="stop-color:#232F3E"/></linearGradient></defs><g><rect x="291" y="63.76" width="400" height="260" rx="39" ry="39" fill="none" stroke="#74b5ed" stroke-dasharray="3 3" pointer-events="all"/><rect x="291" y="402.52" width="400" height="260" rx="39" ry="39" fill="none" stroke="#74b5ed" stroke-dasharray="3 3" pointer-events="all"/><path d="M 516 103.76 L 546 103.76 L 590 183.76 L 613.63 183.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 618.88 183.76 L 611.88 187.26 L 613.63 183.76 L 611.88 180.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 490.82 79.79 C 490.38 79.81 489.94 79.92 489.56 80.12 L 472.37 88.31 C 471.47 88.75 470.81 89.57 470.59 90.53 L 466.34 108.98 C 466.15 109.83 466.31 110.74 466.78 111.48 C 466.86 111.56 466.92 111.64 466.97 111.75 L 478.87 126.53 C 479.5 127.29 480.46 127.76 481.45 127.76 L 500.53 127.76 C 501.51 127.76 502.47 127.29 503.1 126.53 L 515 111.72 C 515.6 110.96 515.85 109.94 515.63 108.98 L 511.38 90.53 C 511.16 89.57 510.5 88.75 509.6 88.31 L 492.41 80.12 C 491.92 79.87 491.37 79.76 490.82 79.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 490.83 81.23 C 490.42 81.25 490.01 81.35 489.65 81.53 L 473.49 89.24 C 472.64 89.65 472.02 90.42 471.82 91.33 L 467.82 108.67 C 467.64 109.47 467.8 110.32 468.23 111.01 C 468.31 111.09 468.36 111.17 468.41 111.27 L 479.6 125.16 C 480.19 125.88 481.09 126.32 482.02 126.32 L 499.95 126.32 C 500.88 126.32 501.78 125.88 502.38 125.16 L 513.56 111.25 C 514.13 110.52 514.36 109.57 514.15 108.67 L 510.16 91.33 C 509.95 90.42 509.33 89.65 508.48 89.24 L 492.33 81.53 C 491.86 81.3 491.35 81.2 490.83 81.23 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 505.54 95.18 L 505.54 110.65 L 492 118.16 L 491.92 99.24 Z M 476.46 95.18 L 476.46 110.65 L 490 118.16 L 490.08 99.24 Z M 476.46 93.58 L 491 89.36 L 505.54 93.58 L 491 97.8 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 359.75 184 L 389.75 184 L 430 104.76 L 453.63 104.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 458.88 104.76 L 451.88 108.26 L 453.63 104.76 L 451.88 101.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 359.75 184 L 389.75 184 L 430 183.76 L 453.63 183.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 458.88 183.76 L 451.88 187.26 L 453.63 183.76 L 451.88 180.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 359.75 184 L 389.75 184 L 430 263.76 L 453.63 263.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 458.88 263.76 L 451.88 267.26 L 453.63 263.76 L 451.88 260.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 334.82 153.79 C 334.38 153.81 333.94 153.92 333.56 154.12 L 316.37 162.31 C 315.47 162.75 314.81 163.57 314.59 164.53 L 310.34 182.98 C 310.15 183.83 310.31 184.74 310.78 185.48 C 310.86 185.56 310.92 185.64 310.97 185.75 L 322.87 200.53 C 323.5 201.29 324.46 201.76 325.45 201.76 L 344.53 201.76 C 345.51 201.76 346.47 201.29 347.1 200.53 L 359 185.72 C 359.6 184.96 359.85 183.94 359.63 182.98 L 355.38 164.53 C 355.16 163.57 354.5 162.75 353.6 162.31 L 336.41 154.12 C 335.92 153.87 335.37 153.76 334.82 153.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 334.83 155.23 C 334.42 155.25 334.01 155.35 333.65 155.53 L 317.49 163.24 C 316.64 163.65 316.02 164.42 315.82 165.33 L 311.82 182.67 C 311.64 183.47 311.8 184.32 312.23 185.01 C 312.31 185.09 312.36 185.17 312.41 185.27 L 323.6 199.16 C 324.19 199.88 325.09 200.32 326.02 200.32 L 343.95 200.32 C 344.88 200.32 345.78 199.88 346.38 199.16 L 357.56 185.25 C 358.13 184.52 358.36 183.57 358.15 182.67 L 354.16 165.33 C 353.95 164.42 353.33 163.65 352.48 163.24 L 336.33 155.53 C 335.86 155.3 335.35 155.2 334.83 155.23 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 324.77 183.3 L 324.77 178.48 L 334.3 178.48 L 334.3 183.3 L 330.98 183.3 L 330.98 188.96 L 339.02 188.96 L 339.02 183.3 L 335.76 183.3 L 335.76 178.48 L 345.25 178.48 L 345.25 183.3 L 341.97 183.3 L 341.97 188.96 L 350 188.96 L 350 183.3 L 346.71 183.3 L 346.71 177.75 C 346.71 177.36 346.37 177.02 345.98 177.02 L 335.73 177.02 L 335.73 172.22 L 341.39 172.22 L 341.39 166.56 L 328.63 166.56 L 328.63 172.22 L 334.27 172.22 L 334.27 172.22 L 334.24 172.22 L 334.24 177.02 L 324.02 177.02 C 323.63 177.02 323.29 177.36 323.29 177.75 L 323.29 183.3 L 320 183.3 L 320 188.96 L 328.03 188.96 L 328.03 183.3 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 175 362.52 L 205 362.52 L 280.25 184 L 303.88 184" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 309.13 184 L 302.13 187.5 L 303.88 184 L 302.13 180.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 230px; margin-left: 262px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><div>udp <br /></div></div></div></div></foreignObject><text x="262" y="234" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">udp &#xa;</text></switch></g><path d="M 175 362.52 L 205 362.52 L 280.25 521.76 L 303.88 521.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 309.13 521.76 L 302.13 525.26 L 303.88 521.76 L 302.13 518.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 92.63 362.76 L 10 362.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 97.88 362.76 L 90.88 366.26 L 92.63 362.76 L 90.88 359.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 355px; margin-left: 50px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">UDP traffic</div></div></div></foreignObject><text x="50" y="359" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">UDP traffic</text></switch></g><rect x="305" y="200.76" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 216px; margin-left: 335px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway <br /></div><div>service</div></div></div></div></foreignObject><text x="335" y="219" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gateway...</text></switch></g><rect x="461" y="126.76" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 142px; margin-left: 491px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway</div>pod</div></div></div></foreignObject><text x="491" y="145" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gatewaypod</text></switch></g><path d="M 516 183.76 L 546 183.76 L 590 183.76 L 613.63 183.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 618.88 183.76 L 611.88 187.26 L 613.63 183.76 L 611.88 180.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 490.82 159.79 C 490.38 159.81 489.94 159.92 489.56 160.12 L 472.37 168.31 C 471.47 168.75 470.81 169.57 470.59 170.53 L 466.34 188.98 C 466.15 189.83 466.31 190.74 466.78 191.48 C 466.86 191.56 466.92 191.64 466.97 191.75 L 478.87 206.53 C 479.5 207.29 480.46 207.76 481.45 207.76 L 500.53 207.76 C 501.51 207.76 502.47 207.29 503.1 206.53 L 515 191.72 C 515.6 190.96 515.85 189.94 515.63 188.98 L 511.38 170.53 C 511.16 169.57 510.5 168.75 509.6 168.31 L 492.41 160.12 C 491.92 159.87 491.37 159.76 490.82 159.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 490.83 161.23 C 490.42 161.25 490.01 161.35 489.65 161.53 L 473.49 169.24 C 472.64 169.65 472.02 170.42 471.82 171.33 L 467.82 188.67 C 467.64 189.47 467.8 190.32 468.23 191.01 C 468.31 191.09 468.36 191.17 468.41 191.27 L 479.6 205.16 C 480.19 205.88 481.09 206.32 482.02 206.32 L 499.95 206.32 C 500.88 206.32 501.78 205.88 502.38 205.16 L 513.56 191.25 C 514.13 190.52 514.36 189.57 514.15 188.67 L 510.16 171.33 C 509.95 170.42 509.33 169.65 508.48 169.24 L 492.33 161.53 C 491.86 161.3 491.35 161.2 490.83 161.23 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 505.54 175.18 L 505.54 190.65 L 492 198.16 L 491.92 179.24 Z M 476.46 175.18 L 476.46 190.65 L 490 198.16 L 490.08 179.24 Z M 476.46 173.58 L 491 169.36 L 505.54 173.58 L 491 177.8 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="461" y="206.76" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 222px; margin-left: 491px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway</div>pod</div></div></div></foreignObject><text x="491" y="225" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gatewaypod</text></switch></g><path d="M 516 263.76 L 546 263.76 L 590 183.76 L 613.63 183.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 618.88 183.76 L 611.88 187.26 L 613.63 183.76 L 611.88 180.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 490.82 239.79 C 490.38 239.81 489.94 239.92 489.56 240.12 L 472.37 248.31 C 471.47 248.75 470.81 249.57 470.59 250.53 L 466.34 268.98 C 466.15 269.83 466.31 270.74 466.78 271.48 C 466.86 271.56 466.92 271.64 466.97 271.75 L 478.87 286.53 C 479.5 287.29 480.46 287.76 481.45 287.76 L 500.53 287.76 C 501.51 287.76 502.47 287.29 503.1 286.53 L 515 271.72 C 515.6 270.96 515.85 269.94 515.63 268.98 L 511.38 250.53 C 511.16 249.57 510.5 248.75 509.6 248.31 L 492.41 240.12 C 491.92 239.87 491.37 239.76 490.82 239.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 490.83 241.23 C 490.42 241.25 490.01 241.35 489.65 241.53 L 473.49 249.24 C 472.64 249.65 472.02 250.42 471.82 251.33 L 467.82 268.67 C 467.64 269.47 467.8 270.32 468.23 271.01 C 468.31 271.09 468.36 271.17 468.41 271.27 L 479.6 285.16 C 480.19 285.88 481.09 286.32 482.02 286.32 L 499.95 286.32 C 500.88 286.32 501.78 285.88 502.38 285.16 L 513.56 271.25 C 514.13 270.52 514.36 269.57 514.15 268.67 L 510.16 251.33 C 509.95 250.42 509.33 249.65 508.48 249.24 L 492.33 241.53 C 491.86 241.3 491.35 241.2 490.83 241.23 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 505.54 255.18 L 505.54 270.65 L 492 278.16 L 491.92 259.24 Z M 476.46 255.18 L 476.46 270.65 L 490 278.16 L 490.08 259.24 Z M 476.46 253.58 L 491 249.36 L 505.54 253.58 L 491 257.8 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="461" y="287.76" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 303px; margin-left: 491px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway</div>pod</div></div></div></foreignObject><text x="491" y="306" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gatewaypod</text></switch></g><path d="M 676 180.76 L 706 180.76 L 800 363.76 L 823.63 363.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 828.88 363.76 L 821.88 367.26 L 823.63 363.76 L 821.88 360.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 650.82 156.79 C 650.38 156.81 649.94 156.92 649.56 157.12 L 632.37 165.31 C 631.47 165.75 630.81 166.57 630.59 167.53 L 626.34 185.98 C 626.15 186.83 626.31 187.74 626.78 188.48 C 626.86 188.56 626.92 188.64 626.97 188.75 L 638.87 203.53 C 639.5 204.29 640.46 204.76 641.45 204.76 L 660.53 204.76 C 661.51 204.76 662.47 204.29 663.1 203.53 L 675 188.72 C 675.6 187.96 675.85 186.94 675.63 185.98 L 671.38 167.53 C 671.16 166.57 670.5 165.75 669.6 165.31 L 652.41 157.12 C 651.92 156.87 651.37 156.76 650.82 156.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 650.83 158.23 C 650.42 158.25 650.01 158.35 649.65 158.53 L 633.49 166.24 C 632.64 166.65 632.02 167.42 631.82 168.33 L 627.82 185.67 C 627.64 186.47 627.8 187.32 628.23 188.01 C 628.31 188.09 628.36 188.17 628.41 188.27 L 639.6 202.16 C 640.19 202.88 641.09 203.32 642.02 203.32 L 659.95 203.32 C 660.88 203.32 661.78 202.88 662.38 202.16 L 673.56 188.25 C 674.13 187.52 674.36 186.57 674.15 185.67 L 670.16 168.33 C 669.95 167.42 669.33 166.65 668.48 166.24 L 652.33 158.53 C 651.86 158.3 651.35 158.2 650.83 158.23 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 640.77 186.3 L 640.77 181.48 L 650.3 181.48 L 650.3 186.3 L 646.98 186.3 L 646.98 191.96 L 655.02 191.96 L 655.02 186.3 L 651.76 186.3 L 651.76 181.48 L 661.25 181.48 L 661.25 186.3 L 657.97 186.3 L 657.97 191.96 L 666 191.96 L 666 186.3 L 662.71 186.3 L 662.71 180.75 C 662.71 180.36 662.37 180.02 661.98 180.02 L 651.73 180.02 L 651.73 175.22 L 657.39 175.22 L 657.39 169.56 L 644.63 169.56 L 644.63 175.22 L 650.27 175.22 L 650.27 175.22 L 650.24 175.22 L 650.24 180.02 L 640.02 180.02 C 639.63 180.02 639.29 180.36 639.29 180.75 L 639.29 186.3 L 636 186.3 L 636 191.96 L 644.03 191.96 L 644.03 186.3 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="621" y="202.76" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 218px; margin-left: 651px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>rabbitMQ<br /></div><div>service</div></div></div></div></foreignObject><text x="651" y="221" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">rabbitMQ...</text></switch></g><path d="M 859.99 388.27 L 855.58 386.15 L 855.58 382.89 L 851.68 384.28 L 848.32 382.65 L 848.32 380.32 L 845.27 381.15 L 842.54 379.84 L 842.54 378.12 L 840.08 378.65 L 837.87 377.58 L 837.87 347.46 L 840.08 346.4 L 842.54 346.93 L 842.54 345.21 L 845.27 343.9 L 848.32 344.72 L 848.32 342.4 L 851.68 340.77 L 855.58 342.15 L 855.58 338.9 L 859.99 336.77 L 882.12 347.46 L 882.12 377.58 Z" fill="#f58534" stroke="none" pointer-events="all"/><path d="M 840.08 378.65 L 837.87 377.58 L 837.87 347.46 L 840.08 346.4 Z M 845.27 381.15 L 842.54 379.84 L 842.54 345.21 L 845.27 343.9 Z M 851.68 384.28 L 848.32 382.65 L 848.32 342.4 L 851.68 340.77 Z M 855.58 386.15 L 855.58 338.9 L 859.99 336.77 L 859.99 388.27 Z" fill-opacity="0.3" fill="#000000" stroke="none" pointer-events="all"/><rect x="100" y="390.27" width="70" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 400px; margin-left: 135px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">NLB UDP</div></div></div></foreignObject><text x="135" y="404" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">NLB UDP</text></switch></g><rect x="830" y="388.27" width="60" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 398px; margin-left: 860px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">rabbitMQ</div></div></div></foreignObject><text x="860" y="402" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">rabbitMQ</text></switch></g><image x="634" y="49.5" width="31" height="29.76" xlink:href="https://app.diagrams.net/img/lib/mscae/Kubernetes.svg"/><rect x="625" y="73.76" width="50" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 84px; margin-left: 650px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font style="font-size: 8px">k8s cluster<br /></font></div></div></div></foreignObject><text x="650" y="87" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">k8s clus...</text></switch></g><path d="M 516 441.52 L 546 441.52 L 590 521.52 L 613.63 521.52" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 618.88 521.52 L 611.88 525.02 L 613.63 521.52 L 611.88 518.02 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 490.82 417.55 C 490.38 417.57 489.94 417.68 489.56 417.88 L 472.37 426.07 C 471.47 426.51 470.81 427.33 470.59 428.29 L 466.34 446.74 C 466.15 447.59 466.31 448.5 466.78 449.24 C 466.86 449.32 466.92 449.4 466.97 449.51 L 478.87 464.29 C 479.5 465.05 480.46 465.52 481.45 465.52 L 500.53 465.52 C 501.51 465.52 502.47 465.05 503.1 464.29 L 515 449.48 C 515.6 448.72 515.85 447.7 515.63 446.74 L 511.38 428.29 C 511.16 427.33 510.5 426.51 509.6 426.07 L 492.41 417.88 C 491.92 417.63 491.37 417.52 490.82 417.55 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 490.83 418.99 C 490.42 419.01 490.01 419.11 489.65 419.29 L 473.49 427 C 472.64 427.41 472.02 428.18 471.82 429.09 L 467.82 446.43 C 467.64 447.23 467.8 448.08 468.23 448.77 C 468.31 448.85 468.36 448.93 468.41 449.03 L 479.6 462.92 C 480.19 463.64 481.09 464.08 482.02 464.08 L 499.95 464.08 C 500.88 464.08 501.78 463.64 502.38 462.92 L 513.56 449.01 C 514.13 448.28 514.36 447.33 514.15 446.43 L 510.16 429.09 C 509.95 428.18 509.33 427.41 508.48 427 L 492.33 419.29 C 491.86 419.06 491.35 418.96 490.83 418.99 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 505.54 432.94 L 505.54 448.41 L 492 455.92 L 491.92 437 Z M 476.46 432.94 L 476.46 448.41 L 490 455.92 L 490.08 437 Z M 476.46 431.34 L 491 427.12 L 505.54 431.34 L 491 435.56 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 359.75 521.76 L 389.75 521.76 L 430 442.52 L 453.63 442.52" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 458.88 442.52 L 451.88 446.02 L 453.63 442.52 L 451.88 439.02 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 359.75 521.76 L 389.75 521.76 L 430 521.52 L 453.63 521.52" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 458.88 521.52 L 451.88 525.02 L 453.63 521.52 L 451.88 518.02 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 359.75 521.76 L 389.75 521.76 L 430 601.52 L 453.63 601.52" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 458.88 601.52 L 451.88 605.02 L 453.63 601.52 L 451.88 598.02 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 334.82 491.55 C 334.38 491.57 333.94 491.68 333.56 491.88 L 316.37 500.07 C 315.47 500.51 314.81 501.33 314.59 502.29 L 310.34 520.74 C 310.15 521.59 310.31 522.5 310.78 523.24 C 310.86 523.32 310.92 523.4 310.97 523.51 L 322.87 538.29 C 323.5 539.05 324.46 539.52 325.45 539.52 L 344.53 539.52 C 345.51 539.52 346.47 539.05 347.1 538.29 L 359 523.48 C 359.6 522.72 359.85 521.7 359.63 520.74 L 355.38 502.29 C 355.16 501.33 354.5 500.51 353.6 500.07 L 336.41 491.88 C 335.92 491.63 335.37 491.52 334.82 491.55 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 334.83 492.99 C 334.42 493.01 334.01 493.11 333.65 493.29 L 317.49 501 C 316.64 501.41 316.02 502.18 315.82 503.09 L 311.82 520.43 C 311.64 521.23 311.8 522.08 312.23 522.77 C 312.31 522.85 312.36 522.93 312.41 523.03 L 323.6 536.92 C 324.19 537.64 325.09 538.08 326.02 538.08 L 343.95 538.08 C 344.88 538.08 345.78 537.64 346.38 536.92 L 357.56 523.01 C 358.13 522.28 358.36 521.33 358.15 520.43 L 354.16 503.09 C 353.95 502.18 353.33 501.41 352.48 501 L 336.33 493.29 C 335.86 493.06 335.35 492.96 334.83 492.99 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 324.77 521.06 L 324.77 516.24 L 334.3 516.24 L 334.3 521.06 L 330.98 521.06 L 330.98 526.72 L 339.02 526.72 L 339.02 521.06 L 335.76 521.06 L 335.76 516.24 L 345.25 516.24 L 345.25 521.06 L 341.97 521.06 L 341.97 526.72 L 350 526.72 L 350 521.06 L 346.71 521.06 L 346.71 515.51 C 346.71 515.12 346.37 514.78 345.98 514.78 L 335.73 514.78 L 335.73 509.98 L 341.39 509.98 L 341.39 504.32 L 328.63 504.32 L 328.63 509.98 L 334.27 509.98 L 334.27 509.98 L 334.24 509.98 L 334.24 514.78 L 324.02 514.78 C 323.63 514.78 323.29 515.12 323.29 515.51 L 323.29 521.06 L 320 521.06 L 320 526.72 L 328.03 526.72 L 328.03 521.06 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="305" y="538.52" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 554px; margin-left: 335px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway <br /></div><div>service</div></div></div></div></foreignObject><text x="335" y="557" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gateway...</text></switch></g><rect x="461" y="464.52" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 480px; margin-left: 491px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway</div>pod</div></div></div></foreignObject><text x="491" y="483" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gatewaypod</text></switch></g><path d="M 516 521.52 L 546 521.52 L 590 521.52 L 613.63 521.52" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 618.88 521.52 L 611.88 525.02 L 613.63 521.52 L 611.88 518.02 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 490.82 497.55 C 490.38 497.57 489.94 497.68 489.56 497.88 L 472.37 506.07 C 471.47 506.51 470.81 507.33 470.59 508.29 L 466.34 526.74 C 466.15 527.59 466.31 528.5 466.78 529.24 C 466.86 529.32 466.92 529.4 466.97 529.51 L 478.87 544.29 C 479.5 545.05 480.46 545.52 481.45 545.52 L 500.53 545.52 C 501.51 545.52 502.47 545.05 503.1 544.29 L 515 529.48 C 515.6 528.72 515.85 527.7 515.63 526.74 L 511.38 508.29 C 511.16 507.33 510.5 506.51 509.6 506.07 L 492.41 497.88 C 491.92 497.63 491.37 497.52 490.82 497.55 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 490.83 498.99 C 490.42 499.01 490.01 499.11 489.65 499.29 L 473.49 507 C 472.64 507.41 472.02 508.18 471.82 509.09 L 467.82 526.43 C 467.64 527.23 467.8 528.08 468.23 528.77 C 468.31 528.85 468.36 528.93 468.41 529.03 L 479.6 542.92 C 480.19 543.64 481.09 544.08 482.02 544.08 L 499.95 544.08 C 500.88 544.08 501.78 543.64 502.38 542.92 L 513.56 529.01 C 514.13 528.28 514.36 527.33 514.15 526.43 L 510.16 509.09 C 509.95 508.18 509.33 507.41 508.48 507 L 492.33 499.29 C 491.86 499.06 491.35 498.96 490.83 498.99 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 505.54 512.94 L 505.54 528.41 L 492 535.92 L 491.92 517 Z M 476.46 512.94 L 476.46 528.41 L 490 535.92 L 490.08 517 Z M 476.46 511.34 L 491 507.12 L 505.54 511.34 L 491 515.56 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="461" y="544.52" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 560px; margin-left: 491px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway</div>pod</div></div></div></foreignObject><text x="491" y="563" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gatewaypod</text></switch></g><path d="M 516 601.52 L 546 601.52 L 590 521.52 L 613.63 521.52" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 618.88 521.52 L 611.88 525.02 L 613.63 521.52 L 611.88 518.02 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 490.82 577.55 C 490.38 577.57 489.94 577.68 489.56 577.88 L 472.37 586.07 C 471.47 586.51 470.81 587.33 470.59 588.29 L 466.34 606.74 C 466.15 607.59 466.31 608.5 466.78 609.24 C 466.86 609.32 466.92 609.4 466.97 609.51 L 478.87 624.29 C 479.5 625.05 480.46 625.52 481.45 625.52 L 500.53 625.52 C 501.51 625.52 502.47 625.05 503.1 624.29 L 515 609.48 C 515.6 608.72 515.85 607.7 515.63 606.74 L 511.38 588.29 C 511.16 587.33 510.5 586.51 509.6 586.07 L 492.41 577.88 C 491.92 577.63 491.37 577.52 490.82 577.55 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 490.83 578.99 C 490.42 579.01 490.01 579.11 489.65 579.29 L 473.49 587 C 472.64 587.41 472.02 588.18 471.82 589.09 L 467.82 606.43 C 467.64 607.23 467.8 608.08 468.23 608.77 C 468.31 608.85 468.36 608.93 468.41 609.03 L 479.6 622.92 C 480.19 623.64 481.09 624.08 482.02 624.08 L 499.95 624.08 C 500.88 624.08 501.78 623.64 502.38 622.92 L 513.56 609.01 C 514.13 608.28 514.36 607.33 514.15 606.43 L 510.16 589.09 C 509.95 588.18 509.33 587.41 508.48 587 L 492.33 579.29 C 491.86 579.06 491.35 578.96 490.83 578.99 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 505.54 592.94 L 505.54 608.41 L 492 615.92 L 491.92 597 Z M 476.46 592.94 L 476.46 608.41 L 490 615.92 L 490.08 597 Z M 476.46 591.34 L 491 587.12 L 505.54 591.34 L 491 595.56 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="461" y="625.52" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 641px; margin-left: 491px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway</div>pod</div></div></div></foreignObject><text x="491" y="644" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gatewaypod</text></switch></g><path d="M 650.82 494.55 C 650.38 494.57 649.94 494.68 649.56 494.88 L 632.37 503.07 C 631.47 503.51 630.81 504.33 630.59 505.29 L 626.34 523.74 C 626.15 524.59 626.31 525.5 626.78 526.24 C 626.86 526.32 626.92 526.4 626.97 526.51 L 638.87 541.29 C 639.5 542.05 640.46 542.52 641.45 542.52 L 660.53 542.52 C 661.51 542.52 662.47 542.05 663.1 541.29 L 675 526.48 C 675.6 525.72 675.85 524.7 675.63 523.74 L 671.38 505.29 C 671.16 504.33 670.5 503.51 669.6 503.07 L 652.41 494.88 C 651.92 494.63 651.37 494.52 650.82 494.55 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 650.83 495.99 C 650.42 496.01 650.01 496.11 649.65 496.29 L 633.49 504 C 632.64 504.41 632.02 505.18 631.82 506.09 L 627.82 523.43 C 627.64 524.23 627.8 525.08 628.23 525.77 C 628.31 525.85 628.36 525.93 628.41 526.03 L 639.6 539.92 C 640.19 540.64 641.09 541.08 642.02 541.08 L 659.95 541.08 C 660.88 541.08 661.78 540.64 662.38 539.92 L 673.56 526.01 C 674.13 525.28 674.36 524.33 674.15 523.43 L 670.16 506.09 C 669.95 505.18 669.33 504.41 668.48 504 L 652.33 496.29 C 651.86 496.06 651.35 495.96 650.83 495.99 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 640.77 524.06 L 640.77 519.24 L 650.3 519.24 L 650.3 524.06 L 646.98 524.06 L 646.98 529.72 L 655.02 529.72 L 655.02 524.06 L 651.76 524.06 L 651.76 519.24 L 661.25 519.24 L 661.25 524.06 L 657.97 524.06 L 657.97 529.72 L 666 529.72 L 666 524.06 L 662.71 524.06 L 662.71 518.51 C 662.71 518.12 662.37 517.78 661.98 517.78 L 651.73 517.78 L 651.73 512.98 L 657.39 512.98 L 657.39 507.32 L 644.63 507.32 L 644.63 512.98 L 650.27 512.98 L 650.27 512.98 L 650.24 512.98 L 650.24 517.78 L 640.02 517.78 C 639.63 517.78 639.29 518.12 639.29 518.51 L 639.29 524.06 L 636 524.06 L 636 529.72 L 644.03 529.72 L 644.03 524.06 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="621" y="540.52" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 556px; margin-left: 651px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>rabbitMQ<br /></div><div>service</div></div></div></div></foreignObject><text x="651" y="559" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">rabbitMQ...</text></switch></g><path d="M 680 523.76 L 710 523.76 L 800 363.76 L 823.63 363.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 828.88 363.76 L 821.88 367.26 L 823.63 363.76 L 821.88 360.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><image x="634" y="387.26" width="31" height="29.76" xlink:href="https://app.diagrams.net/img/lib/mscae/Kubernetes.svg"/><rect x="625" y="411.52" width="50" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 422px; margin-left: 650px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font style="font-size: 8px">k8s cluster<br /></font></div></div></div></foreignObject><text x="650" y="425" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">k8s clus...</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 492px; margin-left: 261px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><div>udp <br /></div></div></div></div></foreignObject><text x="261" y="496" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">udp &#xa;</text></switch></g><path d="M 110 336.77 L 160 336.77 L 160 380 L 110 380 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 111 337.77 L 159 337.77 L 159 385.77 L 111 385.77 Z" fill="url(#mx-gradient-232f3e-100-232f3e-100-s-0)" stroke="none" pointer-events="all"/><path d="M 135 345.27 C 125.89 345.27 118.5 352.66 118.5 361.77 C 118.5 370.88 125.89 378.27 135 378.27 C 144.1 378.27 151.5 370.88 151.5 361.77 C 151.5 352.66 144.1 345.27 135 345.27 Z M 135 346.55 C 143.41 346.55 150.22 353.36 150.22 361.77 C 150.22 370.18 143.41 376.99 135 376.99 C 126.59 376.99 119.78 370.18 119.78 361.77 C 119.78 353.36 126.59 346.55 135 346.55 Z M 140.28 351.87 C 139.93 351.87 139.64 352.16 139.64 352.51 L 139.64 356.43 C 139.64 356.78 139.93 357.07 140.28 357.07 L 144.24 357.07 C 144.59 357.07 144.88 356.78 144.88 356.43 L 144.88 352.51 C 144.88 352.16 144.59 351.87 144.24 351.87 Z M 134.99 353.11 L 134.61 354.34 L 136.1 354.8 L 130.26 357.85 L 130.85 358.99 L 136.71 355.93 L 136.23 357.49 L 137.46 357.86 L 138.38 354.84 C 138.48 354.5 138.29 354.15 137.96 354.04 Z M 140.92 353.15 L 143.6 353.15 L 143.6 355.79 L 140.92 355.79 Z M 122.47 357.79 C 122.3 357.79 122.14 357.86 122.02 357.98 C 121.89 358.1 121.83 358.26 121.83 358.43 L 121.83 365.02 C 121.83 365.37 122.11 365.66 122.47 365.66 L 129.01 365.66 C 129.36 365.66 129.65 365.37 129.65 365.02 L 129.65 358.43 C 129.65 358.26 129.58 358.1 129.46 357.98 C 129.34 357.86 129.18 357.79 129.01 357.79 Z M 136.52 359.04 L 135.62 359.95 L 136.74 361.04 L 131.03 361.1 L 131.05 362.38 L 136.77 362.33 L 135.64 363.5 L 136.56 364.39 L 138.76 362.12 C 138.88 361.99 138.94 361.83 138.94 361.66 C 138.94 361.49 138.87 361.33 138.75 361.21 Z M 140.28 359.05 C 140.11 359.05 139.95 359.12 139.83 359.24 C 139.71 359.36 139.64 359.52 139.64 359.69 L 139.64 363.61 C 139.64 363.78 139.71 363.94 139.83 364.06 C 139.95 364.18 140.11 364.25 140.28 364.25 L 144.24 364.25 C 144.59 364.25 144.88 363.97 144.88 363.61 L 144.88 359.69 C 144.88 359.52 144.81 359.36 144.69 359.24 C 144.57 359.12 144.41 359.05 144.24 359.05 Z M 123.11 359.07 L 128.37 359.07 L 128.37 364.38 L 123.11 364.38 Z M 140.92 360.33 L 143.6 360.33 L 143.6 362.97 L 140.92 362.97 Z M 130.94 364.41 L 130.32 365.54 L 136.04 368.63 L 134.49 369.1 L 134.86 370.33 L 137.88 369.41 C 138.22 369.31 138.41 368.96 138.31 368.62 L 137.43 365.63 L 136.2 366 L 136.65 367.5 Z M 140.28 366.32 C 140.11 366.32 139.95 366.39 139.83 366.51 C 139.71 366.63 139.64 366.79 139.64 366.96 L 139.64 370.88 C 139.64 371.24 139.93 371.52 140.28 371.52 L 144.24 371.52 C 144.59 371.52 144.88 371.24 144.88 370.88 L 144.88 366.96 C 144.88 366.61 144.59 366.32 144.24 366.32 Z M 140.92 367.61 L 143.6 367.61 L 143.6 370.24 L 140.92 370.24 Z" fill="#ffffff" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 150px; margin-left: 407px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><div>udp <br /></div></div></div></div></foreignObject><text x="407" y="153" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">udp &#xa;</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 185px; margin-left: 426px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><div>udp <br /></div></div></div></div></foreignObject><text x="426" y="188" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">udp &#xa;</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 231px; margin-left: 411px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><div>udp <br /></div></div></div></div></foreignObject><text x="411" y="235" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">udp &#xa;</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 478px; margin-left: 410px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><div>udp <br /></div></div></div></div></foreignObject><text x="410" y="481" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">udp &#xa;</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 522px; margin-left: 418px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><div>udp <br /></div></div></div></div></foreignObject><text x="418" y="525" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">udp &#xa;</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 570px; margin-left: 414px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><div>udp <br /></div></div></div></div></foreignObject><text x="414" y="573" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">udp &#xa;</text></switch></g><rect x="291" y="800" width="400" height="260" rx="39" ry="39" fill="none" stroke="#74b5ed" stroke-dasharray="3 3" pointer-events="all"/><rect x="291" y="1138.76" width="400" height="260" rx="39" ry="39" fill="none" stroke="#74b5ed" stroke-dasharray="3 3" pointer-events="all"/><path d="M 516 840 L 546 840 L 590 920 L 613.63 920" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 618.88 920 L 611.88 923.5 L 613.63 920 L 611.88 916.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 490.82 816.03 C 490.38 816.05 489.94 816.16 489.56 816.36 L 472.37 824.55 C 471.47 824.99 470.81 825.81 470.59 826.77 L 466.34 845.22 C 466.15 846.07 466.31 846.98 466.78 847.72 C 466.86 847.8 466.92 847.88 466.97 847.99 L 478.87 862.77 C 479.5 863.53 480.46 864 481.45 864 L 500.53 864 C 501.51 864 502.47 863.53 503.1 862.77 L 515 847.96 C 515.6 847.2 515.85 846.18 515.63 845.22 L 511.38 826.77 C 511.16 825.81 510.5 824.99 509.6 824.55 L 492.41 816.36 C 491.92 816.11 491.37 816 490.82 816.03 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 490.83 817.47 C 490.42 817.49 490.01 817.59 489.65 817.77 L 473.49 825.48 C 472.64 825.89 472.02 826.66 471.82 827.57 L 467.82 844.91 C 467.64 845.71 467.8 846.56 468.23 847.25 C 468.31 847.33 468.36 847.41 468.41 847.51 L 479.6 861.4 C 480.19 862.12 481.09 862.56 482.02 862.56 L 499.95 862.56 C 500.88 862.56 501.78 862.12 502.38 861.4 L 513.56 847.49 C 514.13 846.76 514.36 845.81 514.15 844.91 L 510.16 827.57 C 509.95 826.66 509.33 825.89 508.48 825.48 L 492.33 817.77 C 491.86 817.54 491.35 817.44 490.83 817.47 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 505.54 831.42 L 505.54 846.89 L 492 854.4 L 491.92 835.48 Z M 476.46 831.42 L 476.46 846.89 L 490 854.4 L 490.08 835.48 Z M 476.46 829.82 L 491 825.6 L 505.54 829.82 L 491 834.04 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 359.75 920.24 L 389.75 920.24 L 430 841 L 453.63 841" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 458.88 841 L 451.88 844.5 L 453.63 841 L 451.88 837.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 359.75 920.24 L 389.75 920.24 L 430 920 L 453.63 920" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 458.88 920 L 451.88 923.5 L 453.63 920 L 451.88 916.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 359.75 920.24 L 389.75 920.24 L 430 1000 L 453.63 1000" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 458.88 1000 L 451.88 1003.5 L 453.63 1000 L 451.88 996.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 334.82 890.03 C 334.38 890.05 333.94 890.16 333.56 890.36 L 316.37 898.55 C 315.47 898.99 314.81 899.81 314.59 900.77 L 310.34 919.22 C 310.15 920.07 310.31 920.98 310.78 921.72 C 310.86 921.8 310.92 921.88 310.97 921.99 L 322.87 936.77 C 323.5 937.53 324.46 938 325.45 938 L 344.53 938 C 345.51 938 346.47 937.53 347.1 936.77 L 359 921.96 C 359.6 921.2 359.85 920.18 359.63 919.22 L 355.38 900.77 C 355.16 899.81 354.5 898.99 353.6 898.55 L 336.41 890.36 C 335.92 890.11 335.37 890 334.82 890.03 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 334.83 891.47 C 334.42 891.49 334.01 891.59 333.65 891.77 L 317.49 899.48 C 316.64 899.89 316.02 900.66 315.82 901.57 L 311.82 918.91 C 311.64 919.71 311.8 920.56 312.23 921.25 C 312.31 921.33 312.36 921.41 312.41 921.51 L 323.6 935.4 C 324.19 936.12 325.09 936.56 326.02 936.56 L 343.95 936.56 C 344.88 936.56 345.78 936.12 346.38 935.4 L 357.56 921.49 C 358.13 920.76 358.36 919.81 358.15 918.91 L 354.16 901.57 C 353.95 900.66 353.33 899.89 352.48 899.48 L 336.33 891.77 C 335.86 891.54 335.35 891.44 334.83 891.47 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 324.77 919.54 L 324.77 914.72 L 334.3 914.72 L 334.3 919.54 L 330.98 919.54 L 330.98 925.2 L 339.02 925.2 L 339.02 919.54 L 335.76 919.54 L 335.76 914.72 L 345.25 914.72 L 345.25 919.54 L 341.97 919.54 L 341.97 925.2 L 350 925.2 L 350 919.54 L 346.71 919.54 L 346.71 913.99 C 346.71 913.6 346.37 913.26 345.98 913.26 L 335.73 913.26 L 335.73 908.46 L 341.39 908.46 L 341.39 902.8 L 328.63 902.8 L 328.63 908.46 L 334.27 908.46 L 334.27 908.46 L 334.24 908.46 L 334.24 913.26 L 324.02 913.26 C 323.63 913.26 323.29 913.6 323.29 913.99 L 323.29 919.54 L 320 919.54 L 320 925.2 L 328.03 925.2 L 328.03 919.54 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 175 1098.76 L 205 1098.76 L 280.25 920.24 L 303.88 920.24" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 309.13 920.24 L 302.13 923.74 L 303.88 920.24 L 302.13 916.74 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 967px; margin-left: 262px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">tcp</div></div></div></foreignObject><text x="262" y="970" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">tcp</text></switch></g><path d="M 175 1098.76 L 205 1098.76 L 280.25 1258 L 303.88 1258" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 309.13 1258 L 302.13 1261.5 L 303.88 1258 L 302.13 1254.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 92.63 1099 L 10 1099" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 97.88 1099 L 90.88 1102.5 L 92.63 1099 L 90.88 1095.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1092px; margin-left: 50px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">TCP traffic</div></div></div></foreignObject><text x="50" y="1095" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">TCP traffic</text></switch></g><rect x="305" y="937" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 952px; margin-left: 335px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway <br /></div><div>service</div></div></div></div></foreignObject><text x="335" y="956" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gateway...</text></switch></g><rect x="461" y="863" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 878px; margin-left: 491px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway</div>pod</div></div></div></foreignObject><text x="491" y="882" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gatewaypod</text></switch></g><path d="M 516 920 L 546 920 L 590 920 L 613.63 920" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 618.88 920 L 611.88 923.5 L 613.63 920 L 611.88 916.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 490.82 896.03 C 490.38 896.05 489.94 896.16 489.56 896.36 L 472.37 904.55 C 471.47 904.99 470.81 905.81 470.59 906.77 L 466.34 925.22 C 466.15 926.07 466.31 926.98 466.78 927.72 C 466.86 927.8 466.92 927.88 466.97 927.99 L 478.87 942.77 C 479.5 943.53 480.46 944 481.45 944 L 500.53 944 C 501.51 944 502.47 943.53 503.1 942.77 L 515 927.96 C 515.6 927.2 515.85 926.18 515.63 925.22 L 511.38 906.77 C 511.16 905.81 510.5 904.99 509.6 904.55 L 492.41 896.36 C 491.92 896.11 491.37 896 490.82 896.03 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 490.83 897.47 C 490.42 897.49 490.01 897.59 489.65 897.77 L 473.49 905.48 C 472.64 905.89 472.02 906.66 471.82 907.57 L 467.82 924.91 C 467.64 925.71 467.8 926.56 468.23 927.25 C 468.31 927.33 468.36 927.41 468.41 927.51 L 479.6 941.4 C 480.19 942.12 481.09 942.56 482.02 942.56 L 499.95 942.56 C 500.88 942.56 501.78 942.12 502.38 941.4 L 513.56 927.49 C 514.13 926.76 514.36 925.81 514.15 924.91 L 510.16 907.57 C 509.95 906.66 509.33 905.89 508.48 905.48 L 492.33 897.77 C 491.86 897.54 491.35 897.44 490.83 897.47 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 505.54 911.42 L 505.54 926.89 L 492 934.4 L 491.92 915.48 Z M 476.46 911.42 L 476.46 926.89 L 490 934.4 L 490.08 915.48 Z M 476.46 909.82 L 491 905.6 L 505.54 909.82 L 491 914.04 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="461" y="943" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 958px; margin-left: 491px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway</div>pod</div></div></div></foreignObject><text x="491" y="962" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gatewaypod</text></switch></g><path d="M 516 1000 L 546 1000 L 590 920 L 613.63 920" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 618.88 920 L 611.88 923.5 L 613.63 920 L 611.88 916.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 490.82 976.03 C 490.38 976.05 489.94 976.16 489.56 976.36 L 472.37 984.55 C 471.47 984.99 470.81 985.81 470.59 986.77 L 466.34 1005.22 C 466.15 1006.07 466.31 1006.98 466.78 1007.72 C 466.86 1007.8 466.92 1007.88 466.97 1007.99 L 478.87 1022.77 C 479.5 1023.53 480.46 1024 481.45 1024 L 500.53 1024 C 501.51 1024 502.47 1023.53 503.1 1022.77 L 515 1007.96 C 515.6 1007.2 515.85 1006.18 515.63 1005.22 L 511.38 986.77 C 511.16 985.81 510.5 984.99 509.6 984.55 L 492.41 976.36 C 491.92 976.11 491.37 976 490.82 976.03 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 490.83 977.47 C 490.42 977.49 490.01 977.59 489.65 977.77 L 473.49 985.48 C 472.64 985.89 472.02 986.66 471.82 987.57 L 467.82 1004.91 C 467.64 1005.71 467.8 1006.56 468.23 1007.25 C 468.31 1007.33 468.36 1007.41 468.41 1007.51 L 479.6 1021.4 C 480.19 1022.12 481.09 1022.56 482.02 1022.56 L 499.95 1022.56 C 500.88 1022.56 501.78 1022.12 502.38 1021.4 L 513.56 1007.49 C 514.13 1006.76 514.36 1005.81 514.15 1004.91 L 510.16 987.57 C 509.95 986.66 509.33 985.89 508.48 985.48 L 492.33 977.77 C 491.86 977.54 491.35 977.44 490.83 977.47 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 505.54 991.42 L 505.54 1006.89 L 492 1014.4 L 491.92 995.48 Z M 476.46 991.42 L 476.46 1006.89 L 490 1014.4 L 490.08 995.48 Z M 476.46 989.82 L 491 985.6 L 505.54 989.82 L 491 994.04 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="461" y="1024" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1039px; margin-left: 491px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway</div>pod</div></div></div></foreignObject><text x="491" y="1043" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gatewaypod</text></switch></g><path d="M 676 917 L 706 917 L 800 1100 L 823.63 1100" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 828.88 1100 L 821.88 1103.5 L 823.63 1100 L 821.88 1096.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 650.82 893.03 C 650.38 893.05 649.94 893.16 649.56 893.36 L 632.37 901.55 C 631.47 901.99 630.81 902.81 630.59 903.77 L 626.34 922.22 C 626.15 923.07 626.31 923.98 626.78 924.72 C 626.86 924.8 626.92 924.88 626.97 924.99 L 638.87 939.77 C 639.5 940.53 640.46 941 641.45 941 L 660.53 941 C 661.51 941 662.47 940.53 663.1 939.77 L 675 924.96 C 675.6 924.2 675.85 923.18 675.63 922.22 L 671.38 903.77 C 671.16 902.81 670.5 901.99 669.6 901.55 L 652.41 893.36 C 651.92 893.11 651.37 893 650.82 893.03 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 650.83 894.47 C 650.42 894.49 650.01 894.59 649.65 894.77 L 633.49 902.48 C 632.64 902.89 632.02 903.66 631.82 904.57 L 627.82 921.91 C 627.64 922.71 627.8 923.56 628.23 924.25 C 628.31 924.33 628.36 924.41 628.41 924.51 L 639.6 938.4 C 640.19 939.12 641.09 939.56 642.02 939.56 L 659.95 939.56 C 660.88 939.56 661.78 939.12 662.38 938.4 L 673.56 924.49 C 674.13 923.76 674.36 922.81 674.15 921.91 L 670.16 904.57 C 669.95 903.66 669.33 902.89 668.48 902.48 L 652.33 894.77 C 651.86 894.54 651.35 894.44 650.83 894.47 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 640.77 922.54 L 640.77 917.72 L 650.3 917.72 L 650.3 922.54 L 646.98 922.54 L 646.98 928.2 L 655.02 928.2 L 655.02 922.54 L 651.76 922.54 L 651.76 917.72 L 661.25 917.72 L 661.25 922.54 L 657.97 922.54 L 657.97 928.2 L 666 928.2 L 666 922.54 L 662.71 922.54 L 662.71 916.99 C 662.71 916.6 662.37 916.26 661.98 916.26 L 651.73 916.26 L 651.73 911.46 L 657.39 911.46 L 657.39 905.8 L 644.63 905.8 L 644.63 911.46 L 650.27 911.46 L 650.27 911.46 L 650.24 911.46 L 650.24 916.26 L 640.02 916.26 C 639.63 916.26 639.29 916.6 639.29 916.99 L 639.29 922.54 L 636 922.54 L 636 928.2 L 644.03 928.2 L 644.03 922.54 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="621" y="939" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 954px; margin-left: 651px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>rabbitMQ<br /></div><div>service</div></div></div></div></foreignObject><text x="651" y="958" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">rabbitMQ...</text></switch></g><path d="M 859.99 1124.51 L 855.58 1122.39 L 855.58 1119.13 L 851.68 1120.52 L 848.32 1118.89 L 848.32 1116.56 L 845.27 1117.39 L 842.54 1116.08 L 842.54 1114.36 L 840.08 1114.89 L 837.87 1113.82 L 837.87 1083.7 L 840.08 1082.64 L 842.54 1083.17 L 842.54 1081.45 L 845.27 1080.14 L 848.32 1080.96 L 848.32 1078.64 L 851.68 1077.01 L 855.58 1078.39 L 855.58 1075.14 L 859.99 1073.01 L 882.12 1083.7 L 882.12 1113.82 Z" fill="#f58534" stroke="none" pointer-events="all"/><path d="M 840.08 1114.89 L 837.87 1113.82 L 837.87 1083.7 L 840.08 1082.64 Z M 845.27 1117.39 L 842.54 1116.08 L 842.54 1081.45 L 845.27 1080.14 Z M 851.68 1120.52 L 848.32 1118.89 L 848.32 1078.64 L 851.68 1077.01 Z M 855.58 1122.39 L 855.58 1075.14 L 859.99 1073.01 L 859.99 1124.51 Z" fill-opacity="0.3" fill="#000000" stroke="none" pointer-events="all"/><rect x="80" y="1121.51" width="110" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1137px; margin-left: 135px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>ingress controller</div><div><font size="1"><b style="font-size: 9px">gateway.trackimo.com</b></font><br /></div></div></div></div></foreignObject><text x="135" y="1140" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">ingress controller...</text></switch></g><rect x="830" y="1124.51" width="60" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1135px; margin-left: 860px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">rabbitMQ</div></div></div></foreignObject><text x="860" y="1138" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">rabbitMQ</text></switch></g><image x="634" y="785.74" width="31" height="29.76" xlink:href="https://app.diagrams.net/img/lib/mscae/Kubernetes.svg"/><rect x="625" y="810" width="50" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 820px; margin-left: 650px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font style="font-size: 8px">k8s cluster<br /></font></div></div></div></foreignObject><text x="650" y="824" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">k8s clus...</text></switch></g><path d="M 516 1177.76 L 546 1177.76 L 590 1257.76 L 613.63 1257.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 618.88 1257.76 L 611.88 1261.26 L 613.63 1257.76 L 611.88 1254.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 490.82 1153.79 C 490.38 1153.81 489.94 1153.92 489.56 1154.12 L 472.37 1162.31 C 471.47 1162.75 470.81 1163.57 470.59 1164.53 L 466.34 1182.98 C 466.15 1183.83 466.31 1184.74 466.78 1185.48 C 466.86 1185.56 466.92 1185.64 466.97 1185.75 L 478.87 1200.53 C 479.5 1201.29 480.46 1201.76 481.45 1201.76 L 500.53 1201.76 C 501.51 1201.76 502.47 1201.29 503.1 1200.53 L 515 1185.72 C 515.6 1184.96 515.85 1183.94 515.63 1182.98 L 511.38 1164.53 C 511.16 1163.57 510.5 1162.75 509.6 1162.31 L 492.41 1154.12 C 491.92 1153.87 491.37 1153.76 490.82 1153.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 490.83 1155.23 C 490.42 1155.25 490.01 1155.35 489.65 1155.53 L 473.49 1163.24 C 472.64 1163.65 472.02 1164.42 471.82 1165.33 L 467.82 1182.67 C 467.64 1183.47 467.8 1184.32 468.23 1185.01 C 468.31 1185.09 468.36 1185.17 468.41 1185.27 L 479.6 1199.16 C 480.19 1199.88 481.09 1200.32 482.02 1200.32 L 499.95 1200.32 C 500.88 1200.32 501.78 1199.88 502.38 1199.16 L 513.56 1185.25 C 514.13 1184.52 514.36 1183.57 514.15 1182.67 L 510.16 1165.33 C 509.95 1164.42 509.33 1163.65 508.48 1163.24 L 492.33 1155.53 C 491.86 1155.3 491.35 1155.2 490.83 1155.23 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 505.54 1169.18 L 505.54 1184.65 L 492 1192.16 L 491.92 1173.24 Z M 476.46 1169.18 L 476.46 1184.65 L 490 1192.16 L 490.08 1173.24 Z M 476.46 1167.58 L 491 1163.36 L 505.54 1167.58 L 491 1171.8 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 359.75 1258 L 389.75 1258 L 430 1178.76 L 453.63 1178.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 458.88 1178.76 L 451.88 1182.26 L 453.63 1178.76 L 451.88 1175.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 359.75 1258 L 389.75 1258 L 430 1257.76 L 453.63 1257.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 458.88 1257.76 L 451.88 1261.26 L 453.63 1257.76 L 451.88 1254.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 359.75 1258 L 389.75 1258 L 430 1337.76 L 453.63 1337.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 458.88 1337.76 L 451.88 1341.26 L 453.63 1337.76 L 451.88 1334.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 334.82 1227.79 C 334.38 1227.81 333.94 1227.92 333.56 1228.12 L 316.37 1236.31 C 315.47 1236.75 314.81 1237.57 314.59 1238.53 L 310.34 1256.98 C 310.15 1257.83 310.31 1258.74 310.78 1259.48 C 310.86 1259.56 310.92 1259.64 310.97 1259.75 L 322.87 1274.53 C 323.5 1275.29 324.46 1275.76 325.45 1275.76 L 344.53 1275.76 C 345.51 1275.76 346.47 1275.29 347.1 1274.53 L 359 1259.72 C 359.6 1258.96 359.85 1257.94 359.63 1256.98 L 355.38 1238.53 C 355.16 1237.57 354.5 1236.75 353.6 1236.31 L 336.41 1228.12 C 335.92 1227.87 335.37 1227.76 334.82 1227.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 334.83 1229.23 C 334.42 1229.25 334.01 1229.35 333.65 1229.53 L 317.49 1237.24 C 316.64 1237.65 316.02 1238.42 315.82 1239.33 L 311.82 1256.67 C 311.64 1257.47 311.8 1258.32 312.23 1259.01 C 312.31 1259.09 312.36 1259.17 312.41 1259.27 L 323.6 1273.16 C 324.19 1273.88 325.09 1274.32 326.02 1274.32 L 343.95 1274.32 C 344.88 1274.32 345.78 1273.88 346.38 1273.16 L 357.56 1259.25 C 358.13 1258.52 358.36 1257.57 358.15 1256.67 L 354.16 1239.33 C 353.95 1238.42 353.33 1237.65 352.48 1237.24 L 336.33 1229.53 C 335.86 1229.3 335.35 1229.2 334.83 1229.23 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 324.77 1257.3 L 324.77 1252.48 L 334.3 1252.48 L 334.3 1257.3 L 330.98 1257.3 L 330.98 1262.96 L 339.02 1262.96 L 339.02 1257.3 L 335.76 1257.3 L 335.76 1252.48 L 345.25 1252.48 L 345.25 1257.3 L 341.97 1257.3 L 341.97 1262.96 L 350 1262.96 L 350 1257.3 L 346.71 1257.3 L 346.71 1251.75 C 346.71 1251.36 346.37 1251.02 345.98 1251.02 L 335.73 1251.02 L 335.73 1246.22 L 341.39 1246.22 L 341.39 1240.56 L 328.63 1240.56 L 328.63 1246.22 L 334.27 1246.22 L 334.27 1246.22 L 334.24 1246.22 L 334.24 1251.02 L 324.02 1251.02 C 323.63 1251.02 323.29 1251.36 323.29 1251.75 L 323.29 1257.3 L 320 1257.3 L 320 1262.96 L 328.03 1262.96 L 328.03 1257.3 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="305" y="1274.76" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1290px; margin-left: 335px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway <br /></div><div>service</div></div></div></div></foreignObject><text x="335" y="1293" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gateway...</text></switch></g><rect x="461" y="1200.76" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1216px; margin-left: 491px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway</div>pod</div></div></div></foreignObject><text x="491" y="1219" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gatewaypod</text></switch></g><path d="M 516 1257.76 L 546 1257.76 L 590 1257.76 L 613.63 1257.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 618.88 1257.76 L 611.88 1261.26 L 613.63 1257.76 L 611.88 1254.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 490.82 1233.79 C 490.38 1233.81 489.94 1233.92 489.56 1234.12 L 472.37 1242.31 C 471.47 1242.75 470.81 1243.57 470.59 1244.53 L 466.34 1262.98 C 466.15 1263.83 466.31 1264.74 466.78 1265.48 C 466.86 1265.56 466.92 1265.64 466.97 1265.75 L 478.87 1280.53 C 479.5 1281.29 480.46 1281.76 481.45 1281.76 L 500.53 1281.76 C 501.51 1281.76 502.47 1281.29 503.1 1280.53 L 515 1265.72 C 515.6 1264.96 515.85 1263.94 515.63 1262.98 L 511.38 1244.53 C 511.16 1243.57 510.5 1242.75 509.6 1242.31 L 492.41 1234.12 C 491.92 1233.87 491.37 1233.76 490.82 1233.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 490.83 1235.23 C 490.42 1235.25 490.01 1235.35 489.65 1235.53 L 473.49 1243.24 C 472.64 1243.65 472.02 1244.42 471.82 1245.33 L 467.82 1262.67 C 467.64 1263.47 467.8 1264.32 468.23 1265.01 C 468.31 1265.09 468.36 1265.17 468.41 1265.27 L 479.6 1279.16 C 480.19 1279.88 481.09 1280.32 482.02 1280.32 L 499.95 1280.32 C 500.88 1280.32 501.78 1279.88 502.38 1279.16 L 513.56 1265.25 C 514.13 1264.52 514.36 1263.57 514.15 1262.67 L 510.16 1245.33 C 509.95 1244.42 509.33 1243.65 508.48 1243.24 L 492.33 1235.53 C 491.86 1235.3 491.35 1235.2 490.83 1235.23 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 505.54 1249.18 L 505.54 1264.65 L 492 1272.16 L 491.92 1253.24 Z M 476.46 1249.18 L 476.46 1264.65 L 490 1272.16 L 490.08 1253.24 Z M 476.46 1247.58 L 491 1243.36 L 505.54 1247.58 L 491 1251.8 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="461" y="1280.76" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1296px; margin-left: 491px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway</div>pod</div></div></div></foreignObject><text x="491" y="1299" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gatewaypod</text></switch></g><path d="M 516 1337.76 L 546 1337.76 L 590 1257.76 L 613.63 1257.76" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 618.88 1257.76 L 611.88 1261.26 L 613.63 1257.76 L 611.88 1254.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 490.82 1313.79 C 490.38 1313.81 489.94 1313.92 489.56 1314.12 L 472.37 1322.31 C 471.47 1322.75 470.81 1323.57 470.59 1324.53 L 466.34 1342.98 C 466.15 1343.83 466.31 1344.74 466.78 1345.48 C 466.86 1345.56 466.92 1345.64 466.97 1345.75 L 478.87 1360.53 C 479.5 1361.29 480.46 1361.76 481.45 1361.76 L 500.53 1361.76 C 501.51 1361.76 502.47 1361.29 503.1 1360.53 L 515 1345.72 C 515.6 1344.96 515.85 1343.94 515.63 1342.98 L 511.38 1324.53 C 511.16 1323.57 510.5 1322.75 509.6 1322.31 L 492.41 1314.12 C 491.92 1313.87 491.37 1313.76 490.82 1313.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 490.83 1315.23 C 490.42 1315.25 490.01 1315.35 489.65 1315.53 L 473.49 1323.24 C 472.64 1323.65 472.02 1324.42 471.82 1325.33 L 467.82 1342.67 C 467.64 1343.47 467.8 1344.32 468.23 1345.01 C 468.31 1345.09 468.36 1345.17 468.41 1345.27 L 479.6 1359.16 C 480.19 1359.88 481.09 1360.32 482.02 1360.32 L 499.95 1360.32 C 500.88 1360.32 501.78 1359.88 502.38 1359.16 L 513.56 1345.25 C 514.13 1344.52 514.36 1343.57 514.15 1342.67 L 510.16 1325.33 C 509.95 1324.42 509.33 1323.65 508.48 1323.24 L 492.33 1315.53 C 491.86 1315.3 491.35 1315.2 490.83 1315.23 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 505.54 1329.18 L 505.54 1344.65 L 492 1352.16 L 491.92 1333.24 Z M 476.46 1329.18 L 476.46 1344.65 L 490 1352.16 L 490.08 1333.24 Z M 476.46 1327.58 L 491 1323.36 L 505.54 1327.58 L 491 1331.8 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="461" y="1361.76" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1377px; margin-left: 491px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>gateway</div>pod</div></div></div></foreignObject><text x="491" y="1380" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">gatewaypod</text></switch></g><path d="M 650.82 1230.79 C 650.38 1230.81 649.94 1230.92 649.56 1231.12 L 632.37 1239.31 C 631.47 1239.75 630.81 1240.57 630.59 1241.53 L 626.34 1259.98 C 626.15 1260.83 626.31 1261.74 626.78 1262.48 C 626.86 1262.56 626.92 1262.64 626.97 1262.75 L 638.87 1277.53 C 639.5 1278.29 640.46 1278.76 641.45 1278.76 L 660.53 1278.76 C 661.51 1278.76 662.47 1278.29 663.1 1277.53 L 675 1262.72 C 675.6 1261.96 675.85 1260.94 675.63 1259.98 L 671.38 1241.53 C 671.16 1240.57 670.5 1239.75 669.6 1239.31 L 652.41 1231.12 C 651.92 1230.87 651.37 1230.76 650.82 1230.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 650.83 1232.23 C 650.42 1232.25 650.01 1232.35 649.65 1232.53 L 633.49 1240.24 C 632.64 1240.65 632.02 1241.42 631.82 1242.33 L 627.82 1259.67 C 627.64 1260.47 627.8 1261.32 628.23 1262.01 C 628.31 1262.09 628.36 1262.17 628.41 1262.27 L 639.6 1276.16 C 640.19 1276.88 641.09 1277.32 642.02 1277.32 L 659.95 1277.32 C 660.88 1277.32 661.78 1276.88 662.38 1276.16 L 673.56 1262.25 C 674.13 1261.52 674.36 1260.57 674.15 1259.67 L 670.16 1242.33 C 669.95 1241.42 669.33 1240.65 668.48 1240.24 L 652.33 1232.53 C 651.86 1232.3 651.35 1232.2 650.83 1232.23 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 640.77 1260.3 L 640.77 1255.48 L 650.3 1255.48 L 650.3 1260.3 L 646.98 1260.3 L 646.98 1265.96 L 655.02 1265.96 L 655.02 1260.3 L 651.76 1260.3 L 651.76 1255.48 L 661.25 1255.48 L 661.25 1260.3 L 657.97 1260.3 L 657.97 1265.96 L 666 1265.96 L 666 1260.3 L 662.71 1260.3 L 662.71 1254.75 C 662.71 1254.36 662.37 1254.02 661.98 1254.02 L 651.73 1254.02 L 651.73 1249.22 L 657.39 1249.22 L 657.39 1243.56 L 644.63 1243.56 L 644.63 1249.22 L 650.27 1249.22 L 650.27 1249.22 L 650.24 1249.22 L 650.24 1254.02 L 640.02 1254.02 C 639.63 1254.02 639.29 1254.36 639.29 1254.75 L 639.29 1260.3 L 636 1260.3 L 636 1265.96 L 644.03 1265.96 L 644.03 1260.3 Z" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="621" y="1276.76" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1292px; margin-left: 651px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>rabbitMQ<br /></div><div>service</div></div></div></div></foreignObject><text x="651" y="1295" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">rabbitMQ...</text></switch></g><path d="M 680 1260 L 710 1260 L 800 1100 L 823.63 1100" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 828.88 1100 L 821.88 1103.5 L 823.63 1100 L 821.88 1096.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><image x="634" y="1123.5" width="31" height="29.76" xlink:href="https://app.diagrams.net/img/lib/mscae/Kubernetes.svg"/><rect x="625" y="1147.76" width="50" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1158px; margin-left: 650px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font style="font-size: 8px">k8s cluster<br /></font></div></div></div></foreignObject><text x="650" y="1161" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">k8s clus...</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1228px; margin-left: 261px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">tcp</div></div></div></foreignObject><text x="261" y="1232" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">tcp</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 886px; margin-left: 407px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">tcp</div></div></div></foreignObject><text x="407" y="889" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">tcp</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 921px; margin-left: 426px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">tcp</div></div></div></foreignObject><text x="426" y="924" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">tcp</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 968px; margin-left: 411px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">tcp</div></div></div></foreignObject><text x="411" y="971" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">tcp</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1214px; margin-left: 410px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">tcp</div></div></div></foreignObject><text x="410" y="1217" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">tcp</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1258px; margin-left: 418px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">tcp</div></div></div></foreignObject><text x="418" y="1261" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">tcp</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1306px; margin-left: 414px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">tcp</div></div></div></foreignObject><text x="414" y="1309" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">tcp</text></switch></g><path d="M 134.82 1074.79 C 134.38 1074.81 133.94 1074.92 133.56 1075.12 L 116.37 1083.31 C 115.47 1083.75 114.81 1084.57 114.59 1085.53 L 110.34 1103.98 C 110.15 1104.83 110.31 1105.74 110.78 1106.48 C 110.86 1106.56 110.92 1106.64 110.97 1106.75 L 122.87 1121.53 C 123.5 1122.29 124.46 1122.76 125.45 1122.76 L 144.53 1122.76 C 145.51 1122.76 146.47 1122.29 147.1 1121.53 L 159 1106.72 C 159.6 1105.96 159.85 1104.94 159.63 1103.98 L 155.38 1085.53 C 155.16 1084.57 154.5 1083.75 153.6 1083.31 L 136.41 1075.12 C 135.92 1074.87 135.37 1074.76 134.82 1074.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 134.83 1076.23 C 134.42 1076.25 134.01 1076.35 133.65 1076.53 L 117.49 1084.24 C 116.64 1084.65 116.02 1085.42 115.82 1086.33 L 111.82 1103.67 C 111.64 1104.47 111.8 1105.32 112.23 1106.01 C 112.31 1106.09 112.36 1106.17 112.41 1106.27 L 123.6 1120.16 C 124.19 1120.88 125.09 1121.32 126.02 1121.32 L 143.95 1121.32 C 144.88 1121.32 145.78 1120.88 146.38 1120.16 L 157.56 1106.25 C 158.13 1105.52 158.36 1104.57 158.15 1103.67 L 154.16 1086.33 C 153.95 1085.42 153.33 1084.65 152.48 1084.24 L 136.33 1076.53 C 135.86 1076.3 135.35 1076.2 134.83 1076.23 Z" fill="#2875e2" stroke="none" pointer-events="all"/><path d="M 141.99 1109.12 L 136.19 1109.12 L 123.69 1093.46 L 120.08 1093.46 L 120.08 1088.35 L 125.95 1088.35 L 138.48 1103.98 L 141.99 1103.98 L 141.99 1099.94 L 149.92 1106.55 L 141.99 1113.16 Z M 135.69 1097.02 L 138.48 1093.54 L 141.99 1093.54 L 141.99 1097.58 L 149.92 1090.97 L 141.99 1084.36 L 141.99 1088.4 L 136.19 1088.4 L 132.51 1093.05 Z M 126.54 1100.5 L 123.69 1104.06 L 120.08 1104.06 L 120.08 1109.17 L 125.95 1109.17 L 129.71 1104.47 Z" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 0 700 L 920 700" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><rect x="40" y="0" width="310" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 15px; margin-left: 195px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font style="font-size: 24px" face="Verdana">Data flow for URP traffic<br /></font></div></div></div></foreignObject><text x="195" y="19" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Data flow for URP traffic&#xa;</text></switch></g><rect x="30" y="740" width="310" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 755px; margin-left: 185px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font style="font-size: 24px" face="Verdana">Data flow for URP traffic<br /></font></div></div></div></foreignObject><text x="185" y="759" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Data flow for URP traffic&#xa;</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://desk.draw.io/support/solutions/articles/16000042487" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>