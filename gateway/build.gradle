plugins {
  id 'org.springframework.boot' version '3.2.3'
  id 'io.spring.dependency-management' version '1.1.3'
  id 'java'
  id 'checkstyle'
  id 'application'
}

/* ------------------ Custom ------------------------------------ */
println "Gateway Service Project"
println "Java JDK: " + System.getProperty("java.version")
println "Java home: " + System.getProperty("java.home")
/* -------------------------------------------------------------- */

version = '0.0.1-SNAPSHOT'
version = System.getenv("SERVER_VERSION")
println "Build Version set to : " + version

group = 'com.trackimo'

java {
  sourceCompatibility = JavaVersion.VERSION_21
  targetCompatibility = JavaVersion.VERSION_21
}

configurations {
  compileOnly {
    extendsFrom annotationProcessor
  }
  all*.exclude module:'log4j-api'
}

repositories {
  mavenCentral()
  maven {
    url = uri("https://nexus.trackimo.com/repository/maven-server-spring-boot-3")
    credentials {
      username = System.getenv("NEXUS_USER") ?: getProperty("NEXUS_USER")
      password = System.getenv("NEXUS_PASS") ?: getProperty("NEXUS_PASS")
    }
  }
}

dependencies {
  implementation 'com.trackimo.commons:logs-common:0.1.429-SNAPSHOT'

  implementation 'org.springframework.boot:spring-boot-starter-amqp'
  implementation 'org.springframework.boot:spring-boot-starter-web'
  implementation 'org.springframework.integration:spring-integration-ip:6.5.0'
  implementation 'org.springframework.boot:spring-boot-starter-actuator'
  implementation "org.springframework.data:spring-data-redis"
  implementation "redis.clients:jedis"
  implementation 'org.springframework.cloud:spring-cloud-starter-aws:2.2.1.RELEASE'
//  implementation 'io.micrometer:micrometer-registry-cloudwatch:1.3.2'
//  implementation 'commons-codec:commons-codec:1.18.0'
//  implementation 'jakarta.xml.bind:jakarta.xml.bind-api:4.0.0'
//  implementation 'javax.activation:activation:1.1'
  implementation 'org.glassfish.jaxb:jaxb-runtime:2.3.0'
  implementation 'org.codehaus.janino:janino:3.1.2'
  implementation 'net.logstash.logback:logstash-logback-encoder:7.2'
  testImplementation 'ch.qos.logback:logback-core:1.4.3'
  testImplementation 'ch.qos.logback:logback-classic:1.4.3'

  compileOnly 'org.projectlombok:lombok'
  annotationProcessor 'org.projectlombok:lombok'
  testImplementation('org.springframework.boot:spring-boot-starter-test') {
    exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
  }
  testImplementation 'org.testcontainers:junit-jupiter:1.19.0' // Update testcontainers
  testImplementation 'org.springframework.amqp:spring-rabbit-test'
  testImplementation 'org.assertj:assertj-core:3.14.0'
  testImplementation 'org.testcontainers:testcontainers:1.12.3'
  testImplementation 'org.testcontainers:rabbitmq:1.19.0'
  testImplementation 'org.awaitility:awaitility:4.0.1'
  testImplementation 'net.javacrumbs.json-unit:json-unit-assertj:2.11.1'
}

test {
  useJUnitPlatform {
    excludeTags 'integration'
  }
}

task integrationTest(type: Test) {
  useJUnitPlatform {
    includeTags 'integration'
  }
}

application {
  mainClass = "com.trackimo.gateway.GatewayApplication"
  applicationName = "Gateway"
}

startScripts {
  doLast {
    unixScript.text = unixScript.text.replace('DEFAULT_JVM_OPTS=""', 'JAVA_HOME="$(readlink -m $(which java)/../..)"\nDEFAULT_JVM_OPTS=" -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/data/log/trackimo/ -Dspring.profiles.active=$1 -Dserver.port=8089 -Dserver.address=0.0.0.0 -Dspring.config.additional-location=/data/config/cred-application.properties"')
    //ADD THIS TO CONNECT JMX on DEV port 8765
    // -Djava.rmi.server.hostname=************  -Dcom.sun.management.jmxremote.port=8765 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false
  }
}

jar {
  enabled = true
}