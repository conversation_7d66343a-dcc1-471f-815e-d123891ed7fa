package com.trackimo.gateway.controller.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement(name = "mo-ussd-submit-response")
public class USSDResponseDto {
    private String id;
    private String version;
    private String userData;

    @XmlElement(name = "user-data")
    public String getUserData() {
        return userData;
    }

    @XmlAttribute
    public String getVersion() {
        return version;
    }

    @XmlAttribute
    public String getId() {
        return id;
    }
}
