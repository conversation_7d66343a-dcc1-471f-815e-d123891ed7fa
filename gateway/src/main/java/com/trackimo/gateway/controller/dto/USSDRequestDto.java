package com.trackimo.gateway.controller.dto;

import com.trackimo.gateway.domain.USSDMessage;
import lombok.Data;

import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

@Data
@XmlRootElement(name = "mo-ussd-submit-request")
public class USSDRequestDto {
    private String msisdn;
    private String userData;
    private String imsi;

    @XmlElement(name = "user-data")
    public String getUserData() {
        return userData;
    }

    public USSDMessage toUSSDMessage() {
        return USSDMessage.of(msisdn, userData, imsi);
    }
}
