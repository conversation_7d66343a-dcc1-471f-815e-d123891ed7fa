package com.trackimo.gateway.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Date;

@Controller
public class HealthCheckController {

    private static final Logger logger = LoggerFactory.getLogger(HealthCheckController.class);

    @RequestMapping(value = "/api/build", method = RequestMethod.GET, produces = MediaType.TEXT_HTML_VALUE)
    @ResponseBody
    public String buildCheck() {
        try {
            logger.debug("Build Okay  {} ", new Date());
            final ClassPathResource resource = new ClassPathResource("/build.txt");
            final InputStream inputStream = resource.getInputStream();
            final BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            final StringBuilder line = new StringBuilder();
            while (reader.ready()) {
                line.append("<p>" + reader.readLine() + "</p>");
            }
            return line.toString();
        } catch (Exception e) {
            logger.debug("Build Check Failed  {} ", e);
        }
        return "No File Found";
    }
}
