package com.trackimo.gateway.controller;

import com.trackimo.gateway.domain.UDPMessage;
import com.trackimo.gateway.service.GatewayService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.ip.IpHeaders;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class UDPController {
    private final GatewayService gatewayService;

    public void handleMessage(Message<?> message) {
        try {
            final var headers = message.getHeaders();
            final var remotePort = String.valueOf(headers.get(IpHeaders.PORT));
            final var remoteIp = String.valueOf(headers.get(IpHeaders.IP_ADDRESS));
            final var data = (byte[]) message.getPayload();
            gatewayService.processMessage(
                    new UDPMessage(data, remoteIp, Integer.parseInt(remotePort))
            );
        } catch (Exception e) {
            log.error("Error while procession message from udp to mq: {}", e);
        }
    }
}
