package com.trackimo.gateway.controller;

import com.trackimo.gateway.controller.dto.USSDRequestDto;
import com.trackimo.gateway.controller.dto.USSDResponseDto;
import com.trackimo.gateway.service.GatewayService;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@AllArgsConstructor
public class USSDController {

    private final GatewayService gatewayService;

    @GetMapping(value = "/")
    public ResponseEntity<?> health() {
        return ResponseEntity.ok(Map.of("service", "gateway-ms"));
    }

    @PostMapping(value = "/v1/ussd", produces = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE})
    public USSDResponseDto post(@RequestBody USSDRequestDto dto) {
        gatewayService.processMessage(dto.toUSSDMessage());
        return USSDResponseDto.builder()
            .id("306")
            .version("1.0")
            .userData("Ack")
            .build();
    }

}
