package com.trackimo.gateway.service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.trackimo.gateway.domain.Direction;
import com.trackimo.gateway.domain.Source;
import lombok.Builder;
import lombok.Data;


@Data
@Builder
public class CommUSSDMessageDto {
    private Source source;
    private String code;
    private String data;
    private String msisdn;
    private String imsi;
    private Direction direction;
    @JsonProperty("trk_timeStamp")
    private Long timestamp;
}
