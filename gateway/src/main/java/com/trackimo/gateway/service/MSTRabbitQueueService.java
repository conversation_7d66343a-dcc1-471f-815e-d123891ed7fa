package com.trackimo.gateway.service;

import com.trackimo.gateway.service.dto.CommDataMessageDto;
import com.trackimo.gateway.service.dto.CommUSSDMessageDto;
import lombok.AllArgsConstructor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

@AllArgsConstructor
public class MSTRabbitQueueService implements QueueService {
    private final RabbitTemplate rabbitTemplate;
    private final String exchange;
    private final String routingKey;

    @Override
    public void sendMessage(CommDataMessageDto commDataMessageDto) {
        rabbitTemplate.convertAndSend(exchange, routingKey, commDataMessageDto);
    }

    @Override
    public void sendMessage(CommUSSDMessageDto commUSSDMessageDto) {
        rabbitTemplate.convertAndSend(exchange, routingKey, commUSSDMessageDto);
    }
}
