package com.trackimo.gateway.service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.trackimo.gateway.domain.Channel;
import com.trackimo.gateway.domain.Direction;
import com.trackimo.gateway.domain.Source;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommDataMessageDto {
    private Source source;
    private String serverPort;
    private Integer remotePort;
    private String remoteIp;
    private String data;
    private Direction direction;
    private Channel channel;
    private String sender;
    @JsonProperty("trk_timeStamp")
    private Long timestamp;
    private Long deviceId;
}
