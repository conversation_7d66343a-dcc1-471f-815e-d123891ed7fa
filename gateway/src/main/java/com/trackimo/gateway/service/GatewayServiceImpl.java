package com.trackimo.gateway.service;

import com.trackimo.gateway.domain.Channel;
import com.trackimo.gateway.domain.Direction;
import com.trackimo.gateway.domain.Source;
import com.trackimo.gateway.domain.UDPMessage;
import com.trackimo.gateway.domain.USSDMessage;
import com.trackimo.gateway.service.dto.CommDataMessageDto;
import com.trackimo.gateway.service.dto.CommUSSDMessageDto;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;

@Slf4j
@AllArgsConstructor
public class GatewayServiceImpl implements GatewayService {

    private static String REDIS_MST_KEY = "MST_DEVICES_FOR_COMM";

    private final Integer udpPort;
    private final QueueService queueService;
    private final QueueService mstQueueService;
    private final Times times;
    private final StringRedisTemplate redisTemplate;

    @Override
    public void processMessage(UDPMessage msg) {
        log.info("udp: process msg [{}]", msg.toString());
        final CommDataMessageDto commDataMessage =  CommDataMessageDto.builder()
                .source(Source.DATA)
                .data(msg.getDataHEX())
                .sender(msg.getRemoteIp())
                .remoteIp(msg.getRemoteIp())
                .remotePort(msg.getRemotePort())
                .direction(Direction.CLIENT_TO_SERVER)
                .channel(Channel.DATA)
                .serverPort(udpPort.toString())
                .timestamp(times.timestamp())
                .deviceId(msg.getDeviceId())
                .build();
        log.info("udp: process comm data msg [{}]", commDataMessage);
        if(redisTemplate.opsForHash().hasKey(REDIS_MST_KEY, msg.getDeviceId().toString())) {
            log.info("msg transfer to MST for {}", commDataMessage.getDeviceId());
            mstQueueService.sendMessage(commDataMessage);
            return;
        }
        queueService.sendMessage(commDataMessage);
    }

    @Override
    public void processMessage(USSDMessage msg) {
        log.info("USSD: process msg [{}]", msg.toString());
        queueService.sendMessage(
            CommUSSDMessageDto.builder()
                .source(Source.USSD)
                .code(msg.getCodeFromUSSD())
                .data(msg.getPayloadFromUSSD())
                .imsi(msg.getImsi())
                .msisdn(msg.getMsisdn())
                .direction(Direction.CLIENT_TO_SERVER)
                .timestamp(times.timestamp())
                .build()
        );
    }
}
