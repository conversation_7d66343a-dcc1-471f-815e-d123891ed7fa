package com.trackimo.gateway.telemetry;

import com.trackimo.gateway.domain.UDPMessage;
import com.trackimo.gateway.domain.USSDMessage;
import com.trackimo.gateway.service.GatewayService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

public class MeterGatewayService implements GatewayService {
    private final GatewayService gatewayService;
    private final Counter udpCounter;
    private final Counter ussdCounter;

    public MeterGatewayService(GatewayService gatewayService, MeterRegistry meterRegistry) {
        this.gatewayService = gatewayService;
        this.udpCounter = meterRegistry.counter("gateway.udp");
        this.ussdCounter = meterRegistry.counter("gateway.ussd");
    }

    @Override
    public void processMessage(UDPMessage msg) {
        gatewayService.processMessage(msg);
        udpCounter.increment();
    }

    @Override
    public void processMessage(USSDMessage msg) {
        gatewayService.processMessage(msg);
        ussdCounter.increment();
    }
}
