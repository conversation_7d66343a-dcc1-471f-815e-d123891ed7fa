package com.trackimo.gateway.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.codec.binary.Hex;

import java.math.BigInteger;
import java.util.Arrays;

@Getter
@AllArgsConstructor
public class UDPMessage {
    private final byte[] data;
    private final String remoteIp;
    private final Integer remotePort;

    public String getDataHEX() {
        return Hex.encodeHexString(data, true);
    }

    public Long getDeviceId() {
        return new BigInteger(Arrays.copyOfRange(data, 1, 5)).longValue();
    }

    public Integer getType() {
        return new BigInteger(Arrays.copyOfRange(data, 0, 1)).intValue();
    }

    @Override
    public String toString() {
        return "UDPMessage{" +
                "data=" + Arrays.toString(data) +
                ", remoteIp='" + remoteIp + '\'' +
                ", remotePort=" + remotePort +
                '}';
    }
}
