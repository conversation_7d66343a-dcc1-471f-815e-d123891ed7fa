package com.trackimo.gateway.domain;

import lombok.Data;

@Data
public class USSDMessage {
    private final String msisdn;
    private final String ussd;
    private final String imsi;

    private USSDMessage(String msisdn, String ussd, String imsi) {
        this.msisdn = msisdn;
        this.ussd = ussd;
        this.imsi = imsi;
    }

    public static USSDMessage of(String msisdn, String ussd, String imsi) {
        if (!ussd.matches("\\*+[0-9]+\\*+[0-9]+#$")) {
            throw new IllegalArgumentException(String.format("Not valid ussd [%s]", ussd));
        }
        return new USSDMessage(msisdn, ussd, imsi);
    }

    /**
     * Get payload from ussd, for example:
     * ussd msg: "*126*1001101011010110#" than
     *
     * @return 1001101011010110
     */
    public String getPayloadFromUSSD() {
        return ussd.substring(5, ussd.length() - 1);
    }

    /**
     * Get payload from ussd, for example:
     * ussd msg: "*126*1001101011010110#" than
     *
     * @return 126
     */
    public String getCodeFromUSSD() {
        return ussd.substring(1, 4);
    }
}
