package com.trackimo.gateway;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

import jakarta.annotation.PostConstruct;

@ComponentScan(basePackages = {"com.trackimo.gateway","com.trackimo.commons"})
@Slf4j
@SpringBootApplication
public class GatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(GatewayApplication.class, args);
    }

    @Value("${server.udp.port}")
    private Integer udpPort;

    @PostConstruct
    public void init() {
        log.info("Server listen UDP port :{}", udpPort);
    }
}
