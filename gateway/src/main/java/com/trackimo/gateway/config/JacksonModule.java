package com.trackimo.gateway.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;

import java.io.IOException;
import java.util.function.Function;

public class JacksonModule extends SimpleModule {

    public <T> void addCustomSerializer(Class<T> cls, SerializeFunction<T> serializeFunction) {
        final JsonSerializer<T> jsonSerializer = new JsonSerializer<T>() {
            @Override
            public void serialize(T t, JsonGenerator jgen, SerializerProvider serializerProvider) throws IOException {
                serializeFunction.serialize(t, jgen);
            }
        };
        addSerializer(cls, jsonSerializer);
    }

    public <T> void addStringSerializer(Class<T> cls, Function<T, String> serializeFunction) {
        final JsonSerializer<T> jsonSerializer = new JsonSerializer<T>() {
            @Override
            public void serialize(T t, JsonGenerator jgen, SerializerProvider serializerProvider) throws IOException {
                jgen.writeString(serializeFunction.apply(t));
            }
        };
        addSerializer(cls, jsonSerializer);
    }

    public interface SerializeFunction<T> {
        void serialize(T t, JsonGenerator jgen) throws IOException;
    }
}
