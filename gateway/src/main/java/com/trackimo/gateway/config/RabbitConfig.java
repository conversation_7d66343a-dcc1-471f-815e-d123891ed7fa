package com.trackimo.gateway.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trackimo.gateway.service.QueueService;
import com.trackimo.gateway.service.RabbitQueueService;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class RabbitConfig {

    @Value("${mq.host:localhost}")
    private String mqHost;
    @Value("${mq.user:trackimo}")
    private String mqUser;
    @Value("${mq.password:trackimo}")
    private String mqPassword;

    @Value("${mst.mq.host:localhost}")
    private String mstMqHost;
    @Value("${mst.mq.user:trackimo}")
    private String mstMqUser;
    @Value("${mst.mq.password:trackimo}")
    private String mstMqPassword;

    @Bean
    public QueueService queueService(
        RabbitTemplate rabbitTemplate,
        @Value("${rabbitmq.exchange}") String exchange,
        @Value("${rabbitmq.routing-key}") String routingKey
    ) {
        return new RabbitQueueService(rabbitTemplate, exchange, routingKey);
    }

    @Bean
    @Primary
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory, ObjectMapper objectMapper) {
        final var rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter(objectMapper));
        return rabbitTemplate;
    }

    @Bean
    @Primary
    public ConnectionFactory rabbitConnectionFactory() {
        final CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(mqHost);
        connectionFactory.setUsername(mqUser);
        connectionFactory.setPassword(mqPassword);
        return connectionFactory;
    }

    @Bean("mstRabbitConnectionFactory")
    public ConnectionFactory mstRabbitConnectionFactory() {
        final CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(mstMqHost);
        connectionFactory.setUsername(mstMqUser);
        connectionFactory.setPassword(mstMqPassword);
        return connectionFactory;
    }

    @Bean("mstRabbitTemplate")
    public RabbitTemplate mstRabbitTemplate(ObjectMapper objectMapper) {
        final var rabbitTemplate = new RabbitTemplate(mstRabbitConnectionFactory());
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter(objectMapper));
        return rabbitTemplate;
    }

    @Bean("mstQueueService")
    public QueueService mstQueueService(
            @Value("${rabbitmq.exchange}") String exchange,
            @Value("${rabbitmq.routing-key}") String routingKey
    ) {
        return new RabbitQueueService(mstRabbitTemplate(new ObjectMapper()), exchange, routingKey);
    }
}
