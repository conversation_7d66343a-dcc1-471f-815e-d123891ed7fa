package com.trackimo.gateway.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.trackimo.gateway.service.GatewayService;
import com.trackimo.gateway.service.GatewayServiceImpl;
import com.trackimo.gateway.service.QueueService;
import com.trackimo.gateway.service.Times;
import com.trackimo.gateway.telemetry.MeterGatewayService;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.ip.udp.UnicastReceivingChannelAdapter;

import java.time.Instant;

@Configuration
public class GatewayConfig {

    @Value("${trackimo.gateway.maximumPoolSize:10}")
    private int maximumPoolSize;

    @Bean
    public IntegrationFlow processUniCastUdpMessage(@Value("${server.udp.port}") Integer udpPort) {
        return IntegrationFlow
            .from(createAdapter(udpPort))
            .handle("UDPController", "handleMessage")
            .get();
    }

    private UnicastReceivingChannelAdapter createAdapter(Integer udpPort){
        final UnicastReceivingChannelAdapter adapter = new UnicastReceivingChannelAdapter(udpPort);
        adapter.setPoolSize(maximumPoolSize);
        return adapter;
    }

    @Bean
    public GatewayService gatewayService(
        @Value("${server.udp.port}") Integer udpPort,
        QueueService queueService,
        @Qualifier("mstQueueService")
        QueueService mstQueueService,
        Times times,
        MeterRegistry meterRegistry,
        StringRedisTemplate redisTemplate
    ) {
        return new MeterGatewayService(new GatewayServiceImpl(udpPort, queueService, mstQueueService,
                times, redisTemplate), meterRegistry);
    }

    @Bean
    public Times times() {
        return new Times();
    }

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        final var module = new JacksonModule();
        module.addCustomSerializer(Instant.class, (a, b) -> {
            b.writeNumber(a.getEpochSecond());
        });
        return new ObjectMapper()
            .registerModule(module)
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    }
}
