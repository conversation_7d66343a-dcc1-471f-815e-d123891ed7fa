cloud.aws.stack.auto=false
cloud.aws.region.static=eu-west-1
management.metrics.export.cloudwatch.enabled=false
management.endpoints.web.exposure.include=health, info, metrics, shutdown, env
rabbitmq.exchange=trackimo.java-server
rabbitmq.routing-key=comm.normal
server.udp.port=9997
server.port=8089
spring.metrics.export.cloudwatch.namespace=gateway-ms
spring.metrics.export.cloudwatch.batchSize=20

trackimo.gateway.maximumPoolSize=100
trackimo.logs.serverName=gateway

## TO DO ##
#Elastic Kibana Logstash configuration
logstash.destination.url=*************:5006
logstash.destination.url_p=*************:5007
logstash.destination.url_q=*************:5008
logstash.destination.url_r=*************:5009
elastic.index_name=prod-gateway-service

#spring.redis.host=
#spring.redis.password=null
#spring.redis.port=6379

#FOR RESET LOG LEVEL ONLY
default.log.level=ERROR