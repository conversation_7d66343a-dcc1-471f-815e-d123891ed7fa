cloud.aws.stack.auto=false
cloud.aws.region.static=eu-west-1
management.metrics.export.cloudwatch.enabled=false
management.endpoints.web.exposure.include=health, info, metrics, shutdown, env
rabbitmq.exchange=trackimo.java-server
rabbitmq.routing-key=comm.normal
server.udp.port=9999
server.port=8089
spring.metrics.export.cloudwatch.namespace=gateway-ms
spring.metrics.export.cloudwatch.batchSize=20
trackimo.common.logs.server-name=gateway

mq.host=${RABBIT_HOST:localhost}
mq.user=${RABBIT_USER:trackimo}
mq.password=${RABBIT_PASS:trackimo}

mst.mq.host=${MST_RABBIT_HOST:localhost}
mst.mq.user=${MST_RABBIT_USER:trackimo}
mst.mq.password=${MST_RABBIT_PASS:trackimo}

#Elastic Kibana Logstash configuration
logstash.destination.url=localhost:5001
logstash.destination.url_p=localhost:5010
logstash.destination.url_q=localhost:5009
logstash.destination.url_r=localhost:5008
elastic.index_name=local-gateway


spring.data.redis.host=${REDIS_HOST}
spring.data.redis.port=6379


