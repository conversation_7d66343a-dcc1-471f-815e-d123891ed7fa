cloud.aws.stack.auto=false
cloud.aws.region.static=eu-west-1
management.metrics.export.cloudwatch.enabled=false
management.endpoints.web.exposure.include=health, info, metrics, shutdown, env
rabbitmq.exchange=trackimo.java-server
rabbitmq.routing-key=comm.normal
server.udp.port=9999
server.port=8088
spring.metrics.export.cloudwatch.namespace=gateway-ms
spring.metrics.export.cloudwatch.batchSize=20

trackimo.gateway.maximumPoolSize=100
trackimo.logs.serverName=gateway

## TO DO ##
#Elastic Kibana Logstash configuration
logstash.destination.url=172.31.130.79:5003
logstash.destination.url_p=172.31.130.79:5001
logstash.destination.url_q=172.31.130.79:5002
logstash.destination.url_r=172.31.130.79:5003
elastic.index_name=prod-mst-gateway


#FOR RESET LOG LEVEL ONLY
default.log.level=ERROR