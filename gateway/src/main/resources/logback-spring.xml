<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="true">

    <springProperty name="DESTINATION_URL" source="logstash.destination.url"/>
    <springProperty name="DESTINATION_URL_P" source="logstash.destination.url_p"/>
    <springProperty name="DESTINATION_URL_Q" source="logstash.destination.url_q"/>
    <springProperty name="DESTINATION_URL_R" source="logstash.destination.url_r"/>

    <springProperty name="SERVICE_NAME" source="elastic.index_name"/>
    <property name="LOG_LEVEL" value="INFO"/>

    <springProfile name="local | default">
        <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level value="${LOG_LEVEL}"></level>
            </filter>
            <encoder>
                <pattern>%date{ISO8601} [%thread] %-5level %logger{35} - %msg%n</pattern>
            </encoder>
        </appender>
    </springProfile>

    <springProfile name="dev | stg | stg-watchinu | prod | prod-watchinu | prod-mst | prod-loc8">
        <include resource="org/springframework/boot/logging/logback/base.xml"/>
        <appender name="logstash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">

            <destination>${DESTINATION_URL}</destination>
            <destination>${DESTINATION_URL_P}</destination>
            <destination>${DESTINATION_URL_Q}</destination>
            <destination>${DESTINATION_URL_R}</destination>

            <connectionStrategy>
                <roundRobin>
                    <connectionTTL>5 minutes</connectionTTL>
                </roundRobin>
            </connectionStrategy>

            <keepAliveDuration>5 minutes</keepAliveDuration>

            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <mdc />
                    <context />
                    <logLevel />
                    <loggerName />
                    <pattern >
                        <pattern>
                            {
                            "appName": "${SERVICE_NAME}"
                            }
                        </pattern>
                    </pattern>
                    <threadName />
                    <message />
                    <logstashMarkers />
                    <stackTrace />
                </providers>
            </encoder>
        </appender>
    </springProfile>


    <logger name="com.trackimo.gateway.GatewayApplication" level="INFO"/>
    <logger name="org.springframework.integration" level="WARN"/>
    <logger name="org.spring" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.springframework.boot.autoconfigure" level="ERROR" />
    <logger name="com.trackimo.gateway.service.GatewayServiceImpl" level="INFO"/>


    <springProfile name="dev | stg | stg-watchinu | prod-watchinu | prod-mst | prod | prod-loc8">
        <appender name="ASYNC_LOGSTASH" class="ch.qos.logback.classic.AsyncAppender">
            <discardingThreshold>0</discardingThreshold> <!-- default 20, means drop lower event when has 20% capacity remaining -->
            <appender-ref ref="logstash"/>
            <queueSize>10000</queueSize> <!-- default 256 -->
            <includeCallerData>false</includeCallerData><!-- default false -->
            <neverBlock>true</neverBlock><!-- default false, set to true to cause
                the Appender not block the application and just drop the messages -->
        </appender>
    </springProfile>

    <springProfile name="local | default">
        <root level="${LOG_LEVEL}">
            <appender-ref ref="console"/>
        </root>
    </springProfile>

    <springProfile name="dev | stg | stg-watchinu | prod-watchinu | prod-mst | prod | prod-loc8">
        <root level="${LOG_LEVEL}">
            <appender-ref ref="ASYNC_LOGSTASH"/>
        </root>
    </springProfile>

</configuration>