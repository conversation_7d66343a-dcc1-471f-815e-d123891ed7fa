cloud.aws.stack.auto=false
cloud.aws.region.static=eu-west-1
management.metrics.export.cloudwatch.enabled=false
management.endpoints.web.exposure.include=health, info, metrics, shutdown, env
rabbitmq.exchange=trackimo.java-server
rabbitmq.routing-key=comm.normal
server.udp.port=9999
server.port=8088
spring.metrics.export.cloudwatch.namespace=gateway-ms
spring.metrics.export.cloudwatch.batchSize=20
trackimo.logs.serverName=gateway

#Elastic Kibana Logstash configuration
logstash.destination.url=*************:5003
logstash.destination.url_p=*************:5004
logstash.destination.url_q=*************:5005
logstash.destination.url_r=*************:5000
elastic.index_name=dev-gateway

#spring.redis.host=
#spring.redis.password=null
#spring.redis.port=6379



#FOR RESET LOG LEVEL ONLY
default.log.level=WARN