package com.trackimo.gateway.utils;

import org.testcontainers.shaded.org.apache.commons.io.IOUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

public class Resource {
    private final String path;

    public Resource(String path) {
        this.path = path;
    }

    public String string() throws IOException {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(path)) {
            if (inputStream == null) {
                throw new IllegalArgumentException(String.format("Not found file [%s] on the resources", path));
            }
            return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        }
    }
}
