package com.trackimo.gateway.domain;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class USSDMessageTest {

    private USSDMessage ussdMessage;

    @BeforeEach
    void setUp() {
        ussdMessage = USSDMessage.of("msisdn", "*121*1000101001#", "imsi");
    }

    @Test
    void getPayloadFromUSSD() {
        assertThat(ussdMessage.getPayloadFromUSSD()).isEqualTo("1000101001");
    }

    @Test
    void getCodeFromUSSD() {
        assertThat(ussdMessage.getCodeFromUSSD()).isEqualTo("121");
    }
}
