package com.trackimo.gateway.domain;

import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class UDPMessageTest {

    private UDPMessage msg;

    @BeforeEach
    void setUp() throws DecoderException {
        msg = new UDPMessage(Hex.decodeHex("770f01010fcba8569005db51185f000001fc64"), "123", 123);
    }

    @Test
    void testGetDeviceId() {
        assertThat(msg.getDeviceId()).isEqualTo(251724047L);
    }

    @Test
    void testGetType() {
        assertThat(msg.getType()).isEqualTo(119);
    }
}
