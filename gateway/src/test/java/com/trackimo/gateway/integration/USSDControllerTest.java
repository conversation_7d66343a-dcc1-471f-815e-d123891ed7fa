package com.trackimo.gateway.integration;

import com.trackimo.gateway.service.Times;
import com.trackimo.gateway.utils.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import static java.util.concurrent.TimeUnit.SECONDS;
import static net.javacrumbs.jsonunit.assertj.JsonAssertions.assertThatJson;
import static net.javacrumbs.jsonunit.assertj.JsonAssertions.json;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.Mockito.when;

class USSDControllerTest extends AbstractIntegrationTest {
    private static final String EXCHANGE_NAME = "trackimo.java-server";

    @LocalServerPort
    private Integer serverPort;

    @MockBean
    private Times times;

    @BeforeEach
    void setUp() {
        when(times.timestamp()).thenReturn(1577206L);
    }

    @Test
    void testProcessUSSDMessage() throws Exception {
        final List<String> result = new ArrayList<>();
        exchangeDeclareAndSubscribe(
            EXCHANGE_NAME,
            (consumerTag, delivery) -> result.add(new String(delivery.getBody(), StandardCharsets.UTF_8))
        );

        final var response = sendUSSDMessage(new Resource("test/USSDControllerTest/ussdMsgRequest.xml").string());

        assertThat(response.getStatusCode().value()).isEqualTo(200);
        assertThat(response.getBody())
            .isXmlEqualTo(new Resource("test/USSDControllerTest/ussdMsgResponse.xml").string());
        await().atMost(2, SECONDS).until(() -> {
            if (result.isEmpty()) {
                return false;
            }
            assertThatJson(result.get(0))
                .isEqualTo(json(new Resource("test/USSDControllerTest/testProcessUSSDMessageExpected.json").string()));
            return true;
        });
    }

    private ResponseEntity<String> sendUSSDMessage(String msg) {
        final var headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_XML);
        final var request = new HttpEntity<>(msg, headers);
        final var rest = new RestTemplate();
        return rest.postForEntity(String.format("http://localhost:%s/v1/ussd", serverPort), request, String.class);
    }
}
