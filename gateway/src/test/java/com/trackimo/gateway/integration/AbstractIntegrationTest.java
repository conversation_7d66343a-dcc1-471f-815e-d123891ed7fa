package com.trackimo.gateway.integration;

import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.DeliverCallback;
import com.trackimo.gateway.config.GatewayConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.testcontainers.containers.RabbitMQContainer;

import java.io.IOException;

@Tag("integration")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
public abstract class AbstractIntegrationTest {
    static RabbitMQContainer rabbitMQContainer;
    static ConnectionFactory connectionFactory;

    static {
        try {
            rabbitMQContainer = new RabbitMQContainer();
            rabbitMQContainer.start();
            System.setProperty("rabbitmq.host", rabbitMQContainer.getContainerIpAddress());
            System.setProperty("rabbitmq.port", rabbitMQContainer.getAmqpPort().toString());
            System.setProperty("rabbitmq.username", rabbitMQContainer.getAdminUsername());
            System.setProperty("rabbitmq.password", rabbitMQContainer.getAdminPassword());
        } catch (Throwable t) {
            System.out.println(String.format("Failure during static initialization [%s]", t));
            throw t;
        }
    }

    <T> T jsonToDto(String json, Class<T> clazz) {
        try {
            return new GatewayConfig().objectMapper().readValue(json, clazz);
        } catch (IOException e) {
            throw new AssertionError(e);
        }
    }

    @BeforeEach
    void init() {
        connectionFactory = new ConnectionFactory();
        connectionFactory.setHost(rabbitMQContainer.getContainerIpAddress());
        connectionFactory.setPort(rabbitMQContainer.getAmqpPort());
    }

    void exchangeDeclareAndSubscribe(String exchange, DeliverCallback deliverCallback) throws Exception {
        final var connection = connectionFactory.newConnection();
        final var channel = connection.createChannel();
        channel.exchangeDeclare(exchange, "fanout");
        final var queueName = channel.queueDeclare().getQueue();
        channel.queueBind(queueName, exchange, "");

        channel.basicConsume(queueName, true, deliverCallback, consumerTag -> {
        });
    }
}
