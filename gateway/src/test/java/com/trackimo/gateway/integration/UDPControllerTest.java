package com.trackimo.gateway.integration;

import com.trackimo.gateway.domain.Channel;
import com.trackimo.gateway.domain.Direction;
import com.trackimo.gateway.domain.Source;
import com.trackimo.gateway.service.Times;
import com.trackimo.gateway.service.dto.CommDataMessageDto;
import org.apache.commons.codec.binary.Hex;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import static java.util.concurrent.TimeUnit.SECONDS;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.Mockito.when;

@TestPropertySource(properties = {"rabbitmq.exchange=udp"})
class UDPControllerTest extends AbstractIntegrationTest {
    private static final String EXCHANGE_NAME = "udp";

    @MockBean
    private Times times;

    @Value("${server.udp.port}")
    private Integer udpPort;

    @BeforeEach
    void setUp() {
        when(times.timestamp()).thenReturn(1577206L);
    }

    @Test
    void testHandleMessage() throws Exception {
        final List<String> result = new ArrayList<>();
        exchangeDeclareAndSubscribe(
            EXCHANGE_NAME,
            (consumerTag, delivery) -> result.add(new String(delivery.getBody(), StandardCharsets.UTF_8))
        );
        final var msg = Hex.decodeHex("770f01010fcba8569005db51185f000001fc64");

        sendMessageUsingUDP(msg);

        await().atMost(25, SECONDS).until(() -> {
            if (result.isEmpty()) {
                return false;
            }

            final CommDataMessageDto actual = jsonToDto(result.get(0), CommDataMessageDto.class);
            assertThat(actual.getSource()).isEqualTo(Source.DATA);
            assertThat(actual.getServerPort()).isEqualTo("12345");
            assertThat(actual.getRemotePort()).isNotZero();
            assertThat(actual.getRemoteIp()).isNotEmpty();
            assertThat(actual.getData()).isEqualTo("770f01010fcba8569005db51185f000001fc64");
            assertThat(actual.getDirection()).isEqualTo(Direction.CLIENT_TO_SERVER);
            assertThat(actual.getChannel()).isEqualTo(Channel.DATA);
            assertThat(actual.getSender()).isNotBlank();
            assertThat(actual.getDeviceId()).isEqualTo(251724047);
            assertThat(actual.getTimestamp()).isEqualTo(1577206);

            return true;
        });
    }

    private void sendMessageUsingUDP(byte[] msg) throws IOException {
        try (final DatagramSocket socket = new DatagramSocket(64744)) {
            final DatagramPacket packet = new DatagramPacket(
                msg, msg.length, InetAddress.getByName("localhost"), udpPort
            );
            socket.send(packet);
        }
    }
}
