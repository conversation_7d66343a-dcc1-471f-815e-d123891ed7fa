package com.trackimo.gateway.service;

import com.trackimo.gateway.service.dto.CommDataMessageDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

class RabbitQueueServiceTest {
    private static final String EXCHANGE = "trackimo.java-server";
    private static final String ROUTING_KEY = "comm.normal";

    private RabbitTemplate rabbitTemplate;
    private RabbitQueueService rabbitQueueService;

    @BeforeEach
    void setUp() {
        rabbitTemplate = mock(RabbitTemplate.class);
        rabbitQueueService = new RabbitQueueService(rabbitTemplate, EXCHANGE, ROUTING_KEY);
    }

    @Test
    void testSendMessage() {
        final var device = CommDataMessageDto.builder().deviceId(42L).build();
        rabbitQueueService.sendMessage(device);
        verify(rabbitTemplate).convertAndSend(EXCHANGE, ROUTING_KEY, device);
    }
}
