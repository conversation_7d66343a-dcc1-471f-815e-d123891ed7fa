<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
        "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
        "https://checkstyle.org/dtds/configuration_1_3.dtd">

<module name="Checker">
    <property name="charset" value="UTF-8"/>
    <module name="LineLength">
        <property name="fileExtensions" value="java"/>
        <property name="max" value="120"/>
        <property name="ignorePattern" value="^package.*|^import.*|a href|href|http://|https://|ftp://"/>
    </module>
    <module name="TreeWalker">
        <module name="UnusedImports"/>
        <module name="EmptyBlock"/>
        <module name="EmptyCatchBlock"/>
        <module name="EqualsAvoidNull"/>
        <module name="MissingSwitchDefault"/>
        <module name="AvoidStarImport" />
        <module name="SuppressWarningsHolder" />
        <module name="Indentation">
            <property name="basicOffset" value="4"/>
            <property name="caseIndent" value="4"/>
            <property name="throwsIndent" value="4"/>
            <property name="lineWrappingIndentation" value="4"/>
        </module>
        <module name="OneStatementPerLine"/>
        <module name="FinalLocalVariable"/>
    </module>
    <module name="NewlineAtEndOfFile"/>
    <module name="FileLength"/>
    <module name="FileTabCharacter"/>
    <module name="SuppressWarningsFilter" />
</module>
