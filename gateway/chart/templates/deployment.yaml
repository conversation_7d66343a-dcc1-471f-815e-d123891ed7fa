apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{.Values.service.name}}
  labels:
    app: {{.Values.service.name}}
spec:
  replicas: {{ .Values.service.replicas }}
  selector:
    matchLabels:
      app: {{.Values.service.name}}
  template:
    metadata:
      labels:
        app: {{.Values.service.name}}
    spec:
      containers:
        - name: gateway-ms-udp
          image: {{ .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag }}
          imagePullPolicy: IfNotPresent
          env:
            - name: UDP_PORT
              value: '9999'
            - name: RABBITMQ_HOST
              value: {{ .Values.rabbitmq.host }}
            - name: RABBITMQ_PORT
              value: '5672'
            - name: RABBITMQ_USERNAME
              value: {{ .Values.rabbitmq.username }}
            - name: RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.rabbitmq.password.valueFrom.secretKeyRef.name }}
                  key:  {{ .Values.rabbitmq.password.valueFrom.secretKeyRef.key }}
          ports:
            - name: udp-port
              containerPort: 9999
              protocol: UDP
            - name: tcp-port
              containerPort: 8080
              protocol: TCP
