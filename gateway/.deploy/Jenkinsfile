pipeline {
    agent any
    tools {
        jdk 'Java21'
        gradle 'Gradle 8.9'
    }
    parameters {
        booleanParam(name: "skipTest", defaultValue: false, description: "It will skip test cases running if selected.")
        persistentString(name: 'AWS_ECR_REGION',defaultValue:'us-west-2', description: 'us-west-2,eu-central-1', successfulOnly: false)
        persistentString(name: 'AWS_ACCOUNT_ID',defaultValue:'************', description: 'track<PERSON>(************),Nick<PERSON>atchSRV(************),LOC8(************)', successfulOnly: false)
    }
    environment {
        IMAGE_REPO_NAME                       = "gateway-service"

        AWS_ACCOUNT_ID                        = "${AWS_ACCOUNT_ID}"
        AWS_ECR_REGION                        = "${AWS_ECR_REGION}"

        SERVER_VERSION                        = "${BUILD_ID}"
        GIT_COMMIT                            = "${GIT_COMMIT}"
        GIT_BRANCH                            = "${GIT_BRANCH}"
        TESTCONTAINERS_RYUK_DISABLED          = "true"
        ECR                                   = "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_ECR_REGION}.amazonaws.com"
        ECR_REPO                              = "${ECR}/${IMAGE_REPO_NAME}"
        BUILD_USER_ID                         = "env.BUILD_USER_ID"
        IMAGE_TAG_BY_BUILD_ID                 = "${BUILD_ID}"
        IMAGE_TAG_BY_BRANCH_NAME_AND_BUILD_ID = sh(script: 'echo ${GIT_BRANCH#*/}-$BUILD_ID | sed "s/\\//_/g"', returnStdout: true).trim()
    }
    stages {
        stage('Build spring boot') {
            steps {
                script {
                    wrap([$class: 'BuildUser']) {
                        if (env.skipTest == 'true') {
                            sh 'cd gateway && gradle clean build -x test'
                        } else {
                            sh 'cd gateway && gradle clean build'
                        }
                    }
                }
            }
        }
        stage('Upload to ECR') {
            steps {
                sh 'aws ecr get-login-password --region ${AWS_ECR_REGION} | docker login --username AWS --password-stdin $ECR'
                sh '''
                    docker build \
                        -t $ECR_REPO:$IMAGE_TAG_BY_BUILD_ID \
                        --build-arg SERVER_VERSION=${SERVER_VERSION} \
                        -f gateway/.deploy/Dockerfile .
                '''
                sh 'docker push $ECR_REPO --all-tags'
            }
        }
        stage('CleanUpDocker') {
            steps {
                sh 'docker rmi $ECR_REPO:$IMAGE_TAG_BY_BUILD_ID'
            }
        }
    }
}