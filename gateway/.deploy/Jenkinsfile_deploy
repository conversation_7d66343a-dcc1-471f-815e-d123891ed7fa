pipeline {
    agent any
    parameters {
        persistentString(name: 'SERVER_VERSION', description: 'Note: Please Put Only Number Of the build', successfulOnly: false)
        persistentString(name: 'CLUSTER', description: 'Cluster name (DEV, STG, PROD, Watchinu-Stage, WU-PROD, LOC8)', successfulOnly: false)
        persistentString(name: 'AWS_ACCOUNT_ID', defaultValue:'************', description: 'track<PERSON>(************), <PERSON><PERSON><PERSON><PERSON><PERSON>(************), LOC8(************)', successfulOnly: false)
        persistentString(name: 'AWS_ECR_REGION', defaultValue:'us-west-2', description: 'us-west-2, eu-central-1', successfulOnly: false)
    }
    environment {
        IMAGE_REPO_NAME              = "gateway-service"
        AWS_ACCOUNT_ID               = "${AWS_ACCOUNT_ID}"
        AWS_ECR_REGION               = "${AWS_ECR_REGION}"
        IMAGE_TAG                    = "${SERVER_VERSION}"
        CLUSTER                      = "${CLUSTER}"
        TESTCONTAINERS_RYUK_DISABLED = "true"
        AWS_ECS_REGION               = sh(script: 'if [ ${CLUSTER} = "WU-PROD" ] || [ ${CLUSTER} = "LOC8" ] ; then echo "eu-central-1"; else echo "us-west-2"; fi', returnStdout: true).trim()
        ECR                          = "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_ECR_REGION}.amazonaws.com"
        ECR_REPO                     = "${ECR}/${IMAGE_REPO_NAME}"
        ENV                          = sh(script: """
            if [ "${AWS_ACCOUNT_ID}" = "************" ]; then
                case "${CLUSTER}" in
                    "DEV") echo "dev" ;;
                    "STG") echo "stg" ;;
                    "PROD") echo "prod" ;;
                    "Watchinu-Stage") echo "stg-watchinu" ;;
                    "Japan-Prod") echo "prod-mst" ;;
                    "WU-PROD") echo "prod-watchinu" ;;
                    *) echo "Unknown" ;;
                esac
            elif [ "${AWS_ACCOUNT_ID}" = "************" ]; then
                case "${CLUSTER}" in
                    "WU-PROD") echo "prod-nickwatchsrv" ;;
                    *) echo "Unknown" ;;
                esac
            elif [ "${AWS_ACCOUNT_ID}" = "************" ]; then
                case "${CLUSTER}" in
                    "LOC8") echo "prod-loc8" ;;
                    *) echo "Unknown" ;;
                esac
            else
                echo "Unknown"
            fi
        """, returnStdout: true).trim()
        NEW_TAG = sh(script: 'case ${CLUSTER} in "DEV") echo "develop" ;; "STG") echo "stage" ;; "PROD") echo "master" ;; "Watchinu-Stage") echo "stage" ;; "WU-PROD") echo "master" ;; "Japan-Prod") echo "master" ;; "LOC8") echo "master" ;; *) echo "Unknown" ;; esac', returnStdout: true).trim()
    }

    stages {
        stage('ECS Deploy') {
            steps {
                script {
                    // Assign SERVICE_NAME based on CLUSTER
                    switch(env.CLUSTER) {
                        case "DEV":
                            env.TCP_PORT = 8088
                            env.UDP_PORT = 9999
                            env.SERVICE_NAME = "dev-gateway"
                            env.DESIRED_COUNT = "1"
                            break
                        case "PROD":
                            env.TCP_PORT = 8089
                            env.UDP_PORT = 9997
                            env.SERVICE_NAME = "gateway-service"
                            env.DESIRED_COUNT = "2"
                            break
                        case "Watchinu-Stage":
                            env.TCP_PORT = 8088
                            env.UDP_PORT = 9999
                            env.SERVICE_NAME = "stg-watchinu-gateway"
                            env.DESIRED_COUNT = "1"
                            break
                        case "LOC8":
                            env.TCP_PORT = 8088
                            env.UDP_PORT = 9999
                            env.SERVICE_NAME = "gateway-service"
                            env.DESIRED_COUNT = "1"
                            break
                        case "Japan-Prod":
                            env.TCP_PORT = 8088
                            env.UDP_PORT = 9999
                            env.SERVICE_NAME = "prod-japan-gateway"
                            env.DESIRED_COUNT = "2"
                            break
                        case "WU-PROD":
                            if (env.AWS_ACCOUNT_ID == "************") {
                                env.TCP_PORT = 8088
                                env.UDP_PORT = 9999
                                env.SERVICE_NAME = "gateway-service"
                                env.DESIRED_COUNT = "1"
                            } else {
                                env.TCP_PORT = 8088
                                env.UDP_PORT = 9999
                                env.SERVICE_NAME = "prod-wu-gateway"
                                env.DESIRED_COUNT = "2"
                            }
                            break
                        default:
                            error("Unknown CLUSTER: ${env.CLUSTER}, unable to resolve SERVICE_NAME")
                    }

                    echo "Resolved SERVICE_NAME: ${env.SERVICE_NAME}"
                }

                sh(label: 'ECS deploy', script: '''
                    echo "Replace the container name in the task definition with the new image."
                    envsubst < gateway/.deploy/taskDefinition.json > taskDefinition.json

                    echo "Register task definition"
                    TASK_DEFINITION_ARN=$(aws ecs register-task-definition --region ${AWS_ECS_REGION} --cli-input-json file://taskDefinition.json | \
                    jq '.taskDefinition.taskDefinitionArn' --raw-output)

                    echo "Update ecs service"
                    aws ecs update-service --region ${AWS_ECS_REGION} --cluster ${CLUSTER} --service ${SERVICE_NAME} --task-definition ${TASK_DEFINITION_ARN} --desired-count ${DESIRED_COUNT} --force-new-deployment

                    echo "Update image tag"
                    MANIFEST=$(aws ecr batch-get-image --repository-name ${IMAGE_REPO_NAME} --image-ids imageTag=${IMAGE_TAG} --output text --query 'images[].imageManifest' --region ${AWS_ECR_REGION} )
                    aws ecr put-image --repository-name ${IMAGE_REPO_NAME} --image-tag ${NEW_TAG} --image-manifest "$MANIFEST" --region ${AWS_ECR_REGION}
                ''')
            }
        }
    }
}


