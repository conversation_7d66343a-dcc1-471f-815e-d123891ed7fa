{"executionRoleArn": "arn:aws:iam::${AWS_ACCOUNT_ID}:role/ecsTaskExecutionRole", "containerDefinitions": [{"name": "${SERVICE_NAME}", "image": "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_ECR_REGION}.amazonaws.com/${IMAGE_REPO_NAME}:${IMAGE_TAG}", "cpu": 1024, "memory": 2048, "portMappings": [{"containerPort": "${UDP_PORT}", "hostPort": "${UDP_PORT}", "protocol": "udp"}, {"containerPort": "${TCP_PORT}", "hostPort": "${TCP_PORT}", "protocol": "tcp"}], "environment": [{"name": "SPRING_PROFILES_ACTIVE", "value": "${ENV}"}], "secrets": [{"name": "DB_HOST", "valueFrom": "/${ENV}/db/host"}, {"name": "DB_USER", "valueFrom": "/${ENV}/db/user"}, {"name": "DB_PASS", "valueFrom": "/${ENV}/db/pass"}, {"name": "REDIS_HOST", "valueFrom": "/${ENV}/redis/host"}, {"name": "RABBIT_HOST", "valueFrom": "/${ENV}/rabbit/host"}, {"name": "RABBIT_USER", "valueFrom": "/${ENV}/rabbit/user"}, {"name": "RABBIT_PASS", "valueFrom": "/${ENV}/rabbit/pass"}, {"name": "MST_RABBIT_HOST", "valueFrom": "/${ENV}/rabbit/host"}, {"name": "MST_RABBIT_USER", "valueFrom": "/${ENV}/rabbit/user"}, {"name": "MST_RABBIT_PASS", "valueFrom": "/${ENV}/rabbit/pass"}, {"name": "REDIS_HOST_OLD", "valueFrom": "/${ENV}/redis/host/old"}], "mountPoints": [], "volumesFrom": []}], "family": "${SERVICE_NAME}", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "4096", "placementConstraints": [], "volumes": []}