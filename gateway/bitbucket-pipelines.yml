image: openjdk:11.0.5-jdk-slim

pipelines:
  default:
    - step:
        name: Run Tests
        script:
          - ./gradlew clean test
        artifacts:
          - build/reports/tests/**
    - step:
        name: Run integration test
        script:
          - export TESTCONTAINERS_RYUK_DISABLED=true
          - ./gradlew clean integrationTest
        artifacts:
          - build/reports/tests/test/**
        services:
          - docker
  branches:
    development:
      - step:
          name: Build
          script:
            - ./gradlew clean build -x test
          artifacts:
            - build/**
      - step:
          name: Push to docker registry
          image: trackimo/awscli:1.17.4
          script:
            - eval $(aws ecr get-login --region ${AWS_DEFAULT_REGION} --no-include-email)
            - docker build -t ${AWS_REGISTRY_URL}/trackimo/gateway-ms:${BITBUCKET_COMMIT} .
            - docker push ${AWS_REGISTRY_URL}/trackimo/gateway-ms:${BITBUCKET_COMMIT}
          services:
            - docker
      - step:
          name: Deploy to dev
          deployment: eksdev
          image: trackimo/aws-kubectl-helm:0.1
          script:
            - aws eks --region ${AWS_DEFAULT_REGION} update-kubeconfig --name trackimo-dev
            - helm upgrade -i -n dev gateway-ms ./chart
              --set=image.tag=${BITBUCKET_COMMIT}
              --set=rabbitmq.host=rabbitmq
              --set=rabbitmq.username=trackimo
              --set=rabbitmq.password.valueFrom.secretKeyRef.name=rabbitmq
              --set=rabbitmq.password.valueFrom.secretKeyRef.key=password
              --set=service.nodePort=30662
  custom:
    deploy-to-dev:
      - step:
          name: Deploy to dev
          deployment: dev
          image: trackimo/aws-kubectl-helm:0.1
          script:
            - aws eks --region ${AWS_DEFAULT_REGION} update-kubeconfig --name trackimo-dev
            - helm upgrade -i -n dev gateway-ms ./chart
              --set=image.tag=${BITBUCKET_COMMIT}
              --set=rabbitmq.host=rabbitmq
              --set=rabbitmq.username=trackimo
              --set=rabbitmq.password.valueFrom.secretKeyRef.name=rabbitmq
              --set=rabbitmq.password.valueFrom.secretKeyRef.key=password
              --set=service.nodePort=30662
    deploy-to-eksmob:
      - step:
          name: Deploy to eksdev
          deployment: eksmob
          image: trackimo/aws-kubectl-helm:0.1
          script:
            - aws eks --region ${AWS_DEFAULT_REGION} update-kubeconfig --name trackimo-dev
            - helm upgrade -i -n dev gateway-ms ./chart
              --set=image.tag=${BITBUCKET_COMMIT}
              --set=service.nodePort=31023
    deploy-to-prod:
      - step:
          name: Deploy to prod
          deployment: prod
          image: trackimo/aws-kubectl-helm:0.1
          script:
            - aws eks --region ${AWS_DEFAULT_REGION} update-kubeconfig --name prod
            - helm upgrade -i -n prod gateway-ms ./chart
              --set=image.tag=${BITBUCKET_COMMIT}
              --set=rabbitmq.host=rabbitmq
              --set=rabbitmq.username=trackimo
              --set=rabbitmq.password.valueFrom.secretKeyRef.name=rabbitmq
              --set=rabbitmq.password.valueFrom.secretKeyRef.key=password
              --set=service.nodePort=30551
              --set=service.replicas=4
definitions:
  services:
    docker:
      memory: 2048
