# Gateway

This service can receive messages from different channels and send in RabbitMQ. Service support next channels:

* UDP
* Choice USSD

## How start locally

**Requirements:** Java 11, Docker

1. Login to AWS ECR
```sh
aws ecr get-login-password --region us-west-2 --profile trackimo | docker login --username AWS --password-stdin 052660077578.dkr.ecr.us-west-2.amazonaws.com
```   
2. Start RabbitMQ
```sh 
docker-compose up
```
3. Start service
```sh 
./gradlew bootRun
```

## How to send UDP message
For example, you want to send next hex: `270fa086a700`

If you use MacOS
```sh
echo -ne "\x27\x0f\xa0\x86\xa7\x00" | nc -w1 -cu localhost 9999
```

If you use Linux
```sh
echo -ne "\x27\x0f\xa0\x86\xa7\x00" > /dev/udp/localhost/9999
```

## Data flow

If you notice, on this data flow we do not use the `ingress controller` for UDP, because `ingress` does not support  UDP traffic. Instead of it, we use NLB which will proxy traffic to hardcoded port on the k8s cluster. that's why we should set `service.nodePort` for k8s when we up `helm` chart. For TCP traffic better use `ingress controller`
[img source](https://drive.google.com/file/d/1zCsZQrl6g_CFcJ0fPh5v2-vB4jgzYD--/view?usp=sharing)
![infrastructure](docs/img/infrastructure.svg)

## Deploy using bitbucket
For deploy service make sure that [docker registry]( https://us-west-2.console.aws.amazon.com/ecr/repositories/trackimo/gateway-ms/?region=us-west-2) has image which you whant to deploy. After that choose [commit](https://bitbucket.org/trackimodev/gateway-ms/commits/) what you whant to deploy and press `Run pipeline` for run custom pipeline. 
![img source](docs/img/screenshot-run-pipeline.png)

## Manually deploy

```sh
helm upgrade -i -n dev gateway-ms ./chart \
              --set=image.tag=${GIT_COMMIT}\ 
              --set=rabbitmq.host=rabbitmq \ 
              --set=rabbitmq.username=trackimo \
              --set=rabbitmq.password.valueFrom.secretKeyRef.name=rabbitmq \
              --set=rabbitmq.password.valueFrom.secretKeyRef.key=password \
              --set=service.nodePort=30662 \
              --set=service.replicas=2
```
**notes**

* Make sure that [docker registry]( https://us-west-2.console.aws.amazon.com/ecr/repositories/trackimo/gateway-ms/?region=us-west-2) has `GIT_COMMIT` image
* `service.nodePort` this is port which NLB use for forward internet traffic to k8s claster
* k8s cluster has `RabbitMQ service` which proxy tcp traffic to RabbitMQ ec2 instance. Password for this RabbitMQ stored on the [secrets](https://bitbucket.org/trackimodev/infrastructure/src/master/k8s/dev/secrets.yaml) 

